"use strict";
/**
 * AI-Powered Smart Matching Service for Freela Syria
 * Advanced matching algorithm using machine learning and market intelligence
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.smartMatchingService = exports.SmartMatchingService = void 0;
const supabase_1 = require("@freela/database/src/supabase");
const openRouterService_1 = require("../openRouterService");
class SmartMatchingService {
    openRouter;
    constructor() {
        this.openRouter = new openRouterService_1.OpenRouterService();
    }
    /**
     * Find best expert matches for a project
     */
    async findMatches(criteria, limit = 10) {
        const startTime = Date.now();
        try {
            // Get all available experts
            const experts = await this.getAvailableExperts();
            // Calculate matches using AI-powered scoring
            const matches = await this.calculateMatches(experts, criteria);
            // Sort by match score and limit results
            const sortedMatches = matches
                .sort((a, b) => b.matchScore - a.matchScore)
                .slice(0, limit);
            // Generate market insights
            const marketInsights = await this.generateMarketInsights(criteria, experts);
            const searchTime = Date.now() - startTime;
            return {
                matches: sortedMatches,
                totalExperts: experts.length,
                searchMetadata: {
                    searchTime,
                    algorithm: 'ai-powered-v2',
                    confidence: this.calculateOverallConfidence(sortedMatches),
                },
                marketInsights,
            };
        }
        catch (error) {
            console.error('Error in smart matching:', error);
            throw error;
        }
    }
    /**
     * Get available experts from database
     */
    async getAvailableExperts() {
        const { data: experts, error } = await supabase_1.supabase
            .from('users')
            .select(`
        id,
        first_name,
        last_name,
        avatar_url,
        location,
        expert_profiles!inner (
          title,
          hourly_rate,
          skills,
          experience_years,
          portfolio_items,
          rating,
          completed_projects,
          success_rate,
          response_time_hours,
          availability_status,
          bio
        )
      `)
            .eq('role', 'EXPERT')
            .eq('expert_profiles.availability_status', 'available')
            .gte('expert_profiles.rating', 3.0);
        if (error) {
            console.error('Error fetching experts:', error);
            throw error;
        }
        return experts || [];
    }
    /**
     * Calculate match scores for all experts
     */
    async calculateMatches(experts, criteria) {
        const matches = [];
        for (const expert of experts) {
            try {
                const match = await this.calculateSingleMatch(expert, criteria);
                if (match.matchScore > 0.3) { // Only include reasonable matches
                    matches.push(match);
                }
            }
            catch (error) {
                console.error(`Error calculating match for expert ${expert.id}:`, error);
                // Continue with other experts
            }
        }
        return matches;
    }
    /**
     * Calculate match score for a single expert
     */
    async calculateSingleMatch(expert, criteria) {
        const profile = expert.expert_profiles[0];
        // Calculate individual compatibility scores
        const compatibility = {
            skillMatch: this.calculateSkillMatch(profile.skills, criteria.requiredSkills),
            budgetMatch: this.calculateBudgetMatch(profile.hourly_rate, criteria.budget),
            experienceMatch: this.calculateExperienceMatch(profile.experience_years, criteria.experienceLevel),
            locationMatch: this.calculateLocationMatch(expert.location, criteria.preferredLocation),
            availabilityMatch: this.calculateAvailabilityMatch(profile.response_time_hours),
        };
        // Calculate overall match score using weighted algorithm
        const weights = {
            skillMatch: 0.35,
            budgetMatch: 0.25,
            experienceMatch: 0.20,
            locationMatch: 0.10,
            availabilityMatch: 0.10,
        };
        const matchScore = Object.entries(compatibility).reduce((score, [key, value]) => score + (value * weights[key]), 0);
        // Generate AI insights
        const aiInsights = await this.generateAIInsights(expert, criteria, compatibility);
        // Generate match reasons
        const reasons = this.generateMatchReasons(compatibility, criteria);
        return {
            expertId: expert.id,
            matchScore,
            confidence: this.calculateMatchConfidence(compatibility),
            reasons,
            expert: {
                id: expert.id,
                name: `${expert.first_name} ${expert.last_name}`,
                title: profile.title,
                avatar: expert.avatar_url,
                rating: profile.rating,
                completedProjects: profile.completed_projects,
                skills: profile.skills,
                hourlyRate: profile.hourly_rate,
                location: expert.location,
                responseTime: `${profile.response_time_hours} ساعة`,
                successRate: profile.success_rate,
            },
            compatibility,
            aiInsights,
        };
    }
    /**
     * Calculate skill matching score
     */
    calculateSkillMatch(expertSkills, requiredSkills) {
        if (!expertSkills || !requiredSkills || requiredSkills.length === 0)
            return 0;
        const matchedSkills = requiredSkills.filter(skill => expertSkills.some(expertSkill => expertSkill.toLowerCase().includes(skill.toLowerCase()) ||
            skill.toLowerCase().includes(expertSkill.toLowerCase())));
        return matchedSkills.length / requiredSkills.length;
    }
    /**
     * Calculate budget matching score
     */
    calculateBudgetMatch(expertRate, budget) {
        if (expertRate >= budget.min && expertRate <= budget.max)
            return 1.0;
        const budgetMid = (budget.min + budget.max) / 2;
        const difference = Math.abs(expertRate - budgetMid);
        const tolerance = (budget.max - budget.min) * 0.5;
        return Math.max(0, 1 - (difference / tolerance));
    }
    /**
     * Calculate experience matching score
     */
    calculateExperienceMatch(expertYears, requiredLevel) {
        const levelRequirements = {
            beginner: { min: 0, max: 2 },
            intermediate: { min: 2, max: 5 },
            expert: { min: 5, max: Infinity },
        };
        const requirement = levelRequirements[requiredLevel];
        if (expertYears >= requirement.min && expertYears <= requirement.max)
            return 1.0;
        // Partial match for close experience levels
        if (expertYears >= requirement.min - 1 && expertYears <= requirement.max + 2)
            return 0.7;
        return 0.3;
    }
    /**
     * Calculate location matching score
     */
    calculateLocationMatch(expertLocation, preferredLocation) {
        if (!preferredLocation)
            return 0.8; // Neutral if no preference
        if (expertLocation.toLowerCase().includes(preferredLocation.toLowerCase()))
            return 1.0;
        // Check for same country/region
        const syrianCities = ['دمشق', 'حلب', 'حمص', 'حماة', 'اللاذقية', 'طرطوس', 'درعا', 'السويداء', 'القنيطرة', 'الرقة', 'دير الزور', 'الحسكة', 'إدلب'];
        const isExpertInSyria = syrianCities.some(city => expertLocation.includes(city));
        const isPreferredInSyria = syrianCities.some(city => preferredLocation.includes(city));
        if (isExpertInSyria && isPreferredInSyria)
            return 0.8;
        return 0.5;
    }
    /**
     * Calculate availability matching score
     */
    calculateAvailabilityMatch(responseTimeHours) {
        if (responseTimeHours <= 2)
            return 1.0;
        if (responseTimeHours <= 6)
            return 0.8;
        if (responseTimeHours <= 24)
            return 0.6;
        return 0.4;
    }
    /**
     * Generate AI insights for the match
     */
    async generateAIInsights(expert, criteria, compatibility) {
        try {
            const profile = expert.expert_profiles[0];
            const insightsPrompt = `
تحليل توافق خبير مع مشروع. البيانات:

الخبير:
- الاسم: ${expert.first_name} ${expert.last_name}
- المهارات: ${profile.skills.join(', ')}
- سنوات الخبرة: ${profile.experience_years}
- التقييم: ${profile.rating}/5
- المشاريع المكتملة: ${profile.completed_projects}
- السعر بالساعة: $${profile.hourly_rate}

المشروع:
- الوصف: ${criteria.projectDescription}
- الميزانية: $${criteria.budget.min}-${criteria.budget.max}
- المهارات المطلوبة: ${criteria.requiredSkills.join(', ')}
- مستوى الخبرة المطلوب: ${criteria.experienceLevel}

درجات التوافق:
- توافق المهارات: ${(compatibility.skillMatch * 100).toFixed(0)}%
- توافق الميزانية: ${(compatibility.budgetMatch * 100).toFixed(0)}%
- توافق الخبرة: ${(compatibility.experienceMatch * 100).toFixed(0)}%

يرجى تقديم:
1. نقاط القوة (3-5 نقاط)
2. المخاوف المحتملة (2-3 نقاط)
3. توصية عامة

الرد بتنسيق JSON باللغة العربية.
`;
            const response = await this.openRouter.generateResponse(insightsPrompt, {
                model: 'openai/gpt-4-turbo-preview',
                temperature: 0.6,
                max_tokens: 800,
            });
            return this.parseAIInsights(response);
        }
        catch (error) {
            console.error('Error generating AI insights:', error);
            return {
                strengths: ['خبرة جيدة في المجال'],
                concerns: ['يحتاج تقييم إضافي'],
                recommendation: 'مرشح محتمل للمشروع',
            };
        }
    }
    /**
     * Generate match reasons
     */
    generateMatchReasons(compatibility, criteria) {
        const reasons = [];
        if (compatibility.skillMatch > 0.8) {
            reasons.push('يمتلك جميع المهارات المطلوبة');
        }
        else if (compatibility.skillMatch > 0.6) {
            reasons.push('يمتلك معظم المهارات المطلوبة');
        }
        if (compatibility.budgetMatch > 0.9) {
            reasons.push('السعر ضمن الميزانية المحددة');
        }
        else if (compatibility.budgetMatch > 0.7) {
            reasons.push('السعر قريب من الميزانية المحددة');
        }
        if (compatibility.experienceMatch > 0.8) {
            reasons.push('مستوى الخبرة مناسب للمشروع');
        }
        if (compatibility.availabilityMatch > 0.8) {
            reasons.push('سرعة استجابة ممتازة');
        }
        if (compatibility.locationMatch > 0.8) {
            reasons.push('موقع جغرافي مناسب');
        }
        return reasons.length > 0 ? reasons : ['مرشح محتمل للمشروع'];
    }
    /**
     * Calculate match confidence
     */
    calculateMatchConfidence(compatibility) {
        const scores = Object.values(compatibility);
        const variance = this.calculateVariance(scores);
        const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
        // Higher confidence when scores are consistent and high
        return Math.min(1.0, avgScore * (1 - variance));
    }
    /**
     * Calculate overall confidence for all matches
     */
    calculateOverallConfidence(matches) {
        if (matches.length === 0)
            return 0;
        const avgConfidence = matches.reduce((sum, match) => sum + match.confidence, 0) / matches.length;
        return avgConfidence;
    }
    /**
     * Generate market insights
     */
    async generateMarketInsights(criteria, experts) {
        const rates = experts.map(e => e.expert_profiles[0].hourly_rate).filter(rate => rate > 0);
        const averageRate = rates.reduce((a, b) => a + b, 0) / rates.length;
        // Calculate demand level based on skill availability
        const skillDemand = this.calculateSkillDemand(criteria.requiredSkills, experts);
        return {
            averageRate: Math.round(averageRate),
            demandLevel: skillDemand > 0.7 ? 'high' : skillDemand > 0.4 ? 'medium' : 'low',
            competitionLevel: experts.length,
            recommendedBudget: {
                min: Math.round(averageRate * 0.8),
                max: Math.round(averageRate * 1.3),
            },
        };
    }
    /**
     * Calculate skill demand
     */
    calculateSkillDemand(requiredSkills, experts) {
        const expertsWithSkills = experts.filter(expert => requiredSkills.some(skill => expert.expert_profiles[0].skills.some((expertSkill) => expertSkill.toLowerCase().includes(skill.toLowerCase()))));
        return expertsWithSkills.length / experts.length;
    }
    /**
     * Calculate variance
     */
    calculateVariance(numbers) {
        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
        return squaredDiffs.reduce((a, b) => a + b, 0) / numbers.length;
    }
    /**
     * Parse AI insights response
     */
    parseAIInsights(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return {
                    strengths: parsed.strengths || ['خبرة جيدة'],
                    concerns: parsed.concerns || ['يحتاج تقييم'],
                    recommendation: parsed.recommendation || 'مرشح محتمل',
                };
            }
        }
        catch (error) {
            console.error('Error parsing AI insights:', error);
        }
        return {
            strengths: ['خبرة في المجال'],
            concerns: ['يحتاج مراجعة'],
            recommendation: 'مرشح للمشروع',
        };
    }
}
exports.SmartMatchingService = SmartMatchingService;
// Export singleton instance
exports.smartMatchingService = new SmartMatchingService();
//# sourceMappingURL=smartMatchingService.js.map
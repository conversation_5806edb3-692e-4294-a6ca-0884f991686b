/**
 * Test OpenRouter API Integration
 * Simple test script to verify OpenRouter API connectivity
 */

const axios = require('axios');
require('dotenv').config();

const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';

async function testOpenRouterConnection() {
  console.log('🧪 Testing OpenRouter API Connection...\n');

  try {
    // Test 1: Check available models
    console.log('📋 Test 1: Fetching available models...');
    const modelsResponse = await axios.get(`${OPENROUTER_API_URL}/models`, {
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://freela-syria.com',
        'X-Title': 'Freela Syria AI Test',
      },
      timeout: 10000,
    });

    const models = modelsResponse.data?.data || [];
    console.log(`✅ Found ${models.length} available models`);
    
    // Show first 5 models
    console.log('\n📝 First 5 available models:');
    models.slice(0, 5).forEach((model, index) => {
      console.log(`   ${index + 1}. ${model.id} - ${model.name || 'No name'}`);
    });

    // Test 2: Simple chat completion
    console.log('\n💬 Test 2: Testing chat completion...');
    const chatResponse = await axios.post(`${OPENROUTER_API_URL}/chat/completions`, {
      model: 'openai/gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful AI assistant for Freela Syria, a freelance marketplace. Respond in Arabic.'
        },
        {
          role: 'user',
          content: 'مرحبا، كيف يمكنك مساعدتي؟'
        }
      ],
      temperature: 0.7,
      max_tokens: 150,
    }, {
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://freela-syria.com',
        'X-Title': 'Freela Syria AI Test',
      },
      timeout: 30000,
    });

    const aiResponse = chatResponse.data.choices[0].message.content;
    const usage = chatResponse.data.usage;

    console.log('✅ Chat completion successful!');
    console.log(`📊 Usage: ${usage.prompt_tokens} prompt + ${usage.completion_tokens} completion = ${usage.total_tokens} total tokens`);
    console.log(`🤖 AI Response: ${aiResponse}\n`);

    // Test 3: Arabic conversation test
    console.log('🇸🇾 Test 3: Testing Arabic conversation...');
    const arabicResponse = await axios.post(`${OPENROUTER_API_URL}/chat/completions`, {
      model: 'openai/gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: `أنت مساعد ذكي لمنصة فريلا سوريا، منصة العمل الحر الرائدة في سوريا. مهمتك مساعدة الخبراء في إعداد ملفاتهم المهنية.

السياق السوري:
- تفهم سوق العمل السوري والمهارات المطلوبة
- تدعم اللغة العربية بطلاقة مع فهم اللهجة السورية
- تراعي التحديات المهنية في سوريا

كن ودودًا ومفيدًا ومهنيًا في تفاعلك.`
        },
        {
          role: 'user',
          content: 'أنا مطور ويب من دمشق، أريد إنشاء ملف مهني قوي على المنصة. ما نصائحك؟'
        }
      ],
      temperature: 0.7,
      max_tokens: 300,
    }, {
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://freela-syria.com',
        'X-Title': 'Freela Syria AI Test',
      },
      timeout: 30000,
    });

    const arabicAiResponse = arabicResponse.data.choices[0].message.content;
    const arabicUsage = arabicResponse.data.usage;

    console.log('✅ Arabic conversation test successful!');
    console.log(`📊 Usage: ${arabicUsage.prompt_tokens} prompt + ${arabicUsage.completion_tokens} completion = ${arabicUsage.total_tokens} total tokens`);
    console.log(`🤖 AI Response (Arabic): ${arabicAiResponse}\n`);

    // Summary
    console.log('🎉 All tests passed successfully!');
    console.log('✅ OpenRouter API is working correctly');
    console.log('✅ Arabic language support is functional');
    console.log('✅ Syrian context understanding is working');
    console.log('\n🚀 Ready to implement AI-powered onboarding system!');

  } catch (error) {
    console.error('❌ OpenRouter API test failed:');
    
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Message: ${error.response.data?.error?.message || error.response.statusText}`);
      console.error(`   Details:`, error.response.data);
    } else if (error.request) {
      console.error('   No response received from server');
      console.error('   Check your internet connection and API URL');
    } else {
      console.error(`   Error: ${error.message}`);
    }

    console.log('\n🔧 Troubleshooting tips:');
    console.log('   1. Check your OpenRouter API key');
    console.log('   2. Verify your internet connection');
    console.log('   3. Check if OpenRouter service is available');
    console.log('   4. Review API rate limits');
  }
}

// Run the test
testOpenRouterConnection();

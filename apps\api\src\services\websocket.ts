/**
 * WebSocket Service for Real-time AI Chat
 * Handles real-time communication for AI onboarding conversations
 */

import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { aiConversationService } from './aiConversation';
import { jwtUtils } from '../utils/jwt';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: 'CLIENT' | 'EXPERT' | 'ADMIN';
  sessionId?: string;
}

interface ChatMessage {
  id: string;
  sessionId: string;
  content: string;
  timestamp: Date;
  type: 'user' | 'ai';
}

interface TypingIndicator {
  sessionId: string;
  isTyping: boolean;
  timestamp: Date;
}

class WebSocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, AuthenticatedSocket> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URLS?.split(',') || ['http://localhost:3000'],
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  /**
   * Setup authentication middleware
   */
  private setupMiddleware(): void {
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          logger.warn('WebSocket connection attempted without token', {
            socketId: socket.id,
            ip: socket.handshake.address,
          });
          return next(new Error('Authentication required'));
        }

        // Verify JWT token
        const payload = jwtUtils.verifyAccessToken(token);
        if (!payload) {
          logger.warn('WebSocket connection with invalid token', {
            socketId: socket.id,
            token: token.substring(0, 20) + '...',
          });
          return next(new Error('Invalid token'));
        }

        // Attach user info to socket
        socket.userId = payload.userId;
        socket.userRole = payload.role;

        logger.info('WebSocket connection authenticated', {
          socketId: socket.id,
          userId: socket.userId,
          userRole: socket.userRole,
        });

        next();
      } catch (error: any) {
        logger.error('WebSocket authentication error', {
          socketId: socket.id,
          error: error.message,
        });
        next(new Error('Authentication failed'));
      }
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      this.handleConnection(socket);
    });
  }

  /**
   * Handle new socket connection
   */
  private handleConnection(socket: AuthenticatedSocket): void {
    const { userId, userRole } = socket;
    
    if (!userId) {
      socket.disconnect();
      return;
    }

    // Store connected user
    this.connectedUsers.set(userId, socket);

    logger.info('User connected to WebSocket', {
      socketId: socket.id,
      userId,
      userRole,
      totalConnections: this.connectedUsers.size,
    });

    // Join user to their personal room
    socket.join(`user:${userId}`);

    // Setup event handlers for this socket
    this.setupSocketEventHandlers(socket);

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      this.handleDisconnection(socket, reason);
    });
  }

  /**
   * Setup event handlers for individual socket
   */
  private setupSocketEventHandlers(socket: AuthenticatedSocket): void {
    // Join AI conversation session
    socket.on('join_ai_session', async (data: { sessionId: string }) => {
      try {
        const { sessionId } = data;
        const session = aiConversationService.getSession(sessionId);

        if (!session || session.userId !== socket.userId) {
          socket.emit('error', { message: 'Session not found or access denied' });
          return;
        }

        socket.sessionId = sessionId;
        socket.join(`session:${sessionId}`);

        // Send session data
        socket.emit('session_joined', {
          sessionId,
          currentStep: session.currentStep,
          messages: session.messages,
          extractedData: session.extractedData,
        });

        logger.info('User joined AI session', {
          userId: socket.userId,
          sessionId,
          currentStep: session.currentStep,
        });

      } catch (error: any) {
        logger.error('Error joining AI session', {
          userId: socket.userId,
          error: error.message,
        });
        socket.emit('error', { message: 'Failed to join session' });
      }
    });

    // Send message to AI
    socket.on('send_ai_message', async (data: { sessionId: string; message: string }) => {
      try {
        const { sessionId, message } = data;

        if (!message || message.trim().length === 0) {
          socket.emit('error', { message: 'Message cannot be empty' });
          return;
        }

        // Verify session access
        const session = aiConversationService.getSession(sessionId);
        if (!session || session.userId !== socket.userId) {
          socket.emit('error', { message: 'Session not found or access denied' });
          return;
        }

        // Show typing indicator
        socket.to(`session:${sessionId}`).emit('ai_typing', { isTyping: true });

        // Process message with AI
        const result = await aiConversationService.processMessage(sessionId, message);

        // Hide typing indicator
        socket.to(`session:${sessionId}`).emit('ai_typing', { isTyping: false });

        // Send AI response
        const response = {
          sessionId,
          userMessage: {
            id: `msg_${Date.now()}_user`,
            content: message,
            type: 'user',
            timestamp: new Date(),
          },
          aiMessage: {
            id: `msg_${Date.now()}_ai`,
            content: result.aiResponse.content,
            type: 'ai',
            timestamp: result.aiResponse.timestamp,
          },
          currentStep: result.session.currentStep,
          extractedData: result.session.extractedData,
          isCompleted: result.session.status === 'completed',
        };

        // Send to user
        socket.emit('ai_message_response', response);

        // Also send to session room (for potential observers)
        socket.to(`session:${sessionId}`).emit('ai_message_response', response);

        logger.info('AI message processed via WebSocket', {
          userId: socket.userId,
          sessionId,
          messageLength: message.length,
          currentStep: result.session.currentStep,
        });

      } catch (error: any) {
        logger.error('Error processing AI message via WebSocket', {
          userId: socket.userId,
          sessionId: data.sessionId,
          error: error.message,
        });

        socket.emit('error', { 
          message: 'Failed to process message',
          details: error.message 
        });
      }
    });

    // Handle typing indicators
    socket.on('typing', (data: { sessionId: string; isTyping: boolean }) => {
      const { sessionId, isTyping } = data;
      
      socket.to(`session:${sessionId}`).emit('user_typing', {
        userId: socket.userId,
        isTyping,
        timestamp: new Date(),
      });
    });

    // Start new AI conversation
    socket.on('start_ai_conversation', async (data: { 
      userRole: 'CLIENT' | 'EXPERT';
      language: 'ar' | 'en';
      sessionType?: 'onboarding' | 'profile_optimization' | 'service_creation';
    }) => {
      try {
        const { userRole, language, sessionType = 'onboarding' } = data;

        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Start new conversation
        const session = await aiConversationService.startConversation({
          userId: socket.userId,
          userRole,
          language,
          sessionType,
        });

        // Join session room
        socket.sessionId = session.id;
        socket.join(`session:${session.id}`);

        // Send session data
        socket.emit('conversation_started', {
          sessionId: session.id,
          currentStep: session.currentStep,
          messages: session.messages,
          extractedData: session.extractedData,
        });

        logger.info('New AI conversation started via WebSocket', {
          userId: socket.userId,
          sessionId: session.id,
          userRole,
          language,
          sessionType,
        });

      } catch (error: any) {
        logger.error('Error starting AI conversation via WebSocket', {
          userId: socket.userId,
          error: error.message,
        });

        socket.emit('error', { 
          message: 'Failed to start conversation',
          details: error.message 
        });
      }
    });

    // Get user's conversation sessions
    socket.on('get_user_sessions', () => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        const sessions = aiConversationService.getUserSessions(socket.userId);
        socket.emit('user_sessions', { sessions });

      } catch (error: any) {
        logger.error('Error getting user sessions via WebSocket', {
          userId: socket.userId,
          error: error.message,
        });

        socket.emit('error', { 
          message: 'Failed to get sessions',
          details: error.message 
        });
      }
    });
  }

  /**
   * Handle socket disconnection
   */
  private handleDisconnection(socket: AuthenticatedSocket, reason: string): void {
    const { userId } = socket;

    if (userId) {
      this.connectedUsers.delete(userId);
    }

    logger.info('User disconnected from WebSocket', {
      socketId: socket.id,
      userId,
      reason,
      totalConnections: this.connectedUsers.size,
    });
  }

  /**
   * Send message to specific user
   */
  public sendToUser(userId: string, event: string, data: any): void {
    this.io.to(`user:${userId}`).emit(event, data);
  }

  /**
   * Send message to session participants
   */
  public sendToSession(sessionId: string, event: string, data: any): void {
    this.io.to(`session:${sessionId}`).emit(event, data);
  }

  /**
   * Get connected users count
   */
  public getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  /**
   * Check if user is connected
   */
  public isUserConnected(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }
}

export { WebSocketService };

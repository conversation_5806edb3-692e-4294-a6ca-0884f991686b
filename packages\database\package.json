{"name": "@freela/database", "version": "1.0.0", "description": "Database schema and client for Freela Syria", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:push": "prisma db push"}, "dependencies": {"@prisma/client": "^5.7.0", "@supabase/supabase-js": "^2.39.0", "bcryptjs": "^2.4.3", "nanoid": "^5.0.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22.15.30", "prisma": "^5.7.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "files": ["dist", "prisma/schema.prisma"]}
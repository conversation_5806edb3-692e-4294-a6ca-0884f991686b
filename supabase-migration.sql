-- Freela Syria Database Migration to Supabase
-- This script migrates the complete Prisma schema to Supabase with RLS policies

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types (enums)
CREATE TYPE user_role AS ENUM ('CLIENT', 'EXPERT', 'ADMIN');
CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION');
CREATE TYPE service_status AS ENUM ('DRAFT', 'PENDING_REVIEW', 'ACTIVE', 'PAUSED', 'REJECTED', 'ARCHIVED');
CREATE TYPE booking_status AS ENUM ('PENDING', 'ACCEPTED', 'IN_PROGRESS', 'DELIVERED', 'REVISION_REQUESTED', 'COMPLETED', 'CANCELLED', 'DISPUTED', 'REFUNDED');
CREATE TYPE payment_status AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'DISPUTED', 'CHARGEBACK');
CREATE TYPE payment_method AS ENUM ('CREDIT_CARD', 'DEBIT_CARD', 'PAYPAL', 'BANK_TRANSFER', 'MOBILE_WALLET', 'CRYPTOCURRENCY', 'CASH');
CREATE TYPE notification_type AS ENUM ('BOOKING_REQUEST', 'BOOKING_ACCEPTED', 'BOOKING_REJECTED', 'BOOKING_COMPLETED', 'PAYMENT_RECEIVED', 'MESSAGE_RECEIVED', 'PROFILE_APPROVED', 'SERVICE_APPROVED', 'SYSTEM_ANNOUNCEMENT');

-- Core Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    avatar JSONB,
    role user_role NOT NULL,
    status user_status DEFAULT 'ACTIVE',
    language VARCHAR(10) DEFAULT 'ar',
    location JSONB,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_hash VARCHAR(255) NOT NULL,
    provider VARCHAR(50),
    provider_id VARCHAR(255),
    last_login_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Expert Profiles table
CREATE TABLE expert_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title JSONB NOT NULL,
    description JSONB NOT NULL,
    skills TEXT[] DEFAULT '{}',
    experience VARCHAR(50),
    hourly_rate DECIMAL(10,2),
    availability JSONB,
    response_time VARCHAR(50),
    completed_projects INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    verified BOOLEAN DEFAULT FALSE,
    verification_documents JSONB[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Client Profiles table
CREATE TABLE client_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_name VARCHAR(255),
    company_size VARCHAR(50),
    industry VARCHAR(100),
    projects_posted INTEGER DEFAULT 0,
    total_spent DECIMAL(12,2) DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Service Categories table
CREATE TABLE service_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name JSONB NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description JSONB,
    icon VARCHAR(255),
    parent_id UUID REFERENCES service_categories(id),
    service_count INTEGER DEFAULT 0,
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Services table
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    expert_id UUID NOT NULL REFERENCES expert_profiles(id) ON DELETE CASCADE,
    title JSONB NOT NULL,
    description JSONB NOT NULL,
    category_id UUID NOT NULL REFERENCES service_categories(id),
    subcategory VARCHAR(255),
    tags TEXT[] DEFAULT '{}',
    images JSONB[] DEFAULT '{}',
    pricing JSONB NOT NULL,
    delivery_time INTEGER NOT NULL,
    revisions INTEGER NOT NULL,
    requirements JSONB[] DEFAULT '{}',
    add_ons JSONB[] DEFAULT '{}',
    status service_status DEFAULT 'DRAFT',
    featured BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    order_count INTEGER DEFAULT 0,
    last_order_at TIMESTAMPTZ,
    seo_slug VARCHAR(255) UNIQUE NOT NULL,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Conversation Sessions table
CREATE TABLE ai_conversation_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_type VARCHAR(50) NOT NULL,
    user_role user_role NOT NULL,
    language VARCHAR(10) DEFAULT 'ar',
    current_step VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    ai_model VARCHAR(100) DEFAULT 'openai/gpt-4-turbo-preview',
    temperature DECIMAL(3,2) DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 1000,
    completion_rate DECIMAL(3,2) DEFAULT 0.0,
    steps_completed TEXT[] DEFAULT '{}',
    total_steps INTEGER DEFAULT 0,
    extracted_data JSONB DEFAULT '{}',
    profile_data JSONB,
    service_data JSONB,
    recommendations JSONB[] DEFAULT '{}',
    started_at TIMESTAMPTZ DEFAULT NOW(),
    last_active_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    estimated_duration INTEGER,
    actual_duration INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Conversation Messages table
CREATE TABLE ai_conversation_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES ai_conversation_sessions(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    content_arabic TEXT,
    message_type VARCHAR(50) DEFAULT 'text',
    step_name VARCHAR(100),
    intent VARCHAR(100),
    confidence DECIMAL(3,2),
    ai_model VARCHAR(100),
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    processing_time INTEGER,
    user_feedback TEXT,
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    flagged BOOLEAN DEFAULT FALSE,
    flag_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Extracted Data table
CREATE TABLE ai_extracted_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES ai_conversation_sessions(id) ON DELETE CASCADE,
    data_type VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    original_text TEXT NOT NULL,
    extracted_value JSONB NOT NULL,
    normalized_value TEXT,
    confidence DECIMAL(3,2) NOT NULL,
    validated BOOLEAN DEFAULT FALSE,
    validated_by UUID REFERENCES users(id),
    validated_at TIMESTAMPTZ,
    validation_notes TEXT,
    extraction_method VARCHAR(50) NOT NULL,
    ai_model VARCHAR(100),
    processing_version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Recommendations table
CREATE TABLE ai_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES ai_conversation_sessions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    type VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium',
    title VARCHAR(200) NOT NULL,
    title_arabic VARCHAR(200),
    description TEXT NOT NULL,
    description_arabic TEXT,
    recommendation_data JSONB NOT NULL,
    action_required JSONB,
    expected_impact TEXT,
    confidence_score DECIMAL(3,2) NOT NULL,
    market_analysis JSONB,
    competitor_analysis JSONB,
    success_probability DECIMAL(3,2),
    status VARCHAR(20) DEFAULT 'pending',
    user_feedback TEXT,
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    implemented_at TIMESTAMPTZ,
    viewed_at TIMESTAMPTZ,
    responded_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Market Intelligence table
CREATE TABLE ai_market_intelligence (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    region VARCHAR(50) DEFAULT 'syria',
    average_price DECIMAL(10,2),
    price_range JSONB,
    pricing_model VARCHAR(50),
    currency VARCHAR(10) DEFAULT 'USD',
    demand_level VARCHAR(20),
    demand_trend VARCHAR(20),
    seasonality JSONB,
    competitor_count INTEGER,
    competition_level VARCHAR(20),
    top_skills TEXT[] DEFAULT '{}',
    opportunities JSONB[] DEFAULT '{}',
    threats JSONB[] DEFAULT '{}',
    recommendations JSONB[] DEFAULT '{}',
    data_source VARCHAR(100) NOT NULL,
    data_quality DECIMAL(3,2) DEFAULT 0.8,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    valid_until TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_expert_profiles_user_id ON expert_profiles(user_id);
CREATE INDEX idx_services_expert_id ON services(expert_id);
CREATE INDEX idx_services_category_id ON services(category_id);
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_ai_sessions_user_id ON ai_conversation_sessions(user_id);
CREATE INDEX idx_ai_sessions_status ON ai_conversation_sessions(status);
CREATE INDEX idx_ai_messages_session_id ON ai_conversation_messages(session_id);
CREATE INDEX idx_ai_messages_created_at ON ai_conversation_messages(created_at);
CREATE INDEX idx_ai_extracted_data_session_id ON ai_extracted_data(session_id);
CREATE INDEX idx_ai_recommendations_user_id ON ai_recommendations(user_id);
CREATE INDEX idx_ai_recommendations_status ON ai_recommendations(status);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE expert_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_conversation_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_conversation_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_extracted_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;

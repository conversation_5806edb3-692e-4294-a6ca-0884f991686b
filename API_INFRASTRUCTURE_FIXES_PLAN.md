# 🔧 API Infrastructure Fixes - Detailed Implementation Plan
# خطة إصلاح البنية التحتية لواجهة برمجة التطبيقات

## 📋 **OVERVIEW**

**Objective**: Resolve all 29 TypeScript compilation errors and missing infrastructure  
**Priority**: **CRITICAL** - Blocking all development  
**Timeline**: 5 days  
**Dependencies**: None (foundational fixes)

---

## 🚨 **CRITICAL ISSUES BREAKDOWN**

### **TypeScript Compilation Errors: 29 Total**

#### **File: `apps/api/src/routes/ai.ts` (19 errors)**
```typescript
// Current Issues:
- validateRequest import error (should be validateBody)
- Missing createError utility
- Missing asyncHandler utility
- 16 implicit 'any' type parameters in route handlers
```

#### **File: `apps/api/src/services/ai/enhancedAIService.ts` (1 error)**
```typescript
// Current Issue:
- Cannot find module './openRouterService'
// Should be: '../openRouterService'
```

#### **File: `apps/api/src/services/ai/smartMatchingService.ts` (1 error)**
```typescript
// Current Issue:
- Cannot find module '../openRouterService'
// Should be: '../openrouter' (existing file)
```

#### **File: `apps/api/src/services/aiConversation.ts` (2 errors)**
```typescript
// Current Issues:
- Missing createError utility
- Type mismatch in stepKeys.indexOf()
```

#### **File: `apps/api/src/services/openrouter.ts` (1 error)**
```typescript
// Current Issue:
- Missing createError utility
```

#### **File: `apps/api/src/services/websocket.ts` (1 error)**
```typescript
// Current Issue:
- Missing jwtUtils utility
```

#### **File: `apps/api/src/tests/aiIntegrationTest.ts` (2 errors)**
```typescript
// Current Issues:
- Wrong import path for OpenRouterService
- Implicit 'any' type in array method
```

#### **File: `packages/database/src/supabase.ts` (2 errors)**
```typescript
// Current Issues:
- Missing @supabase/supabase-js dependency
- Implicit 'any' type in callback
```

---

## 🛠️ **IMPLEMENTATION PLAN**

### **Phase 1: Create Missing Utility Files (Day 1-2)**

#### **1.1 Create Error Handling Utility**
**File**: `apps/api/src/utils/errors.ts`

```typescript
// Structure Overview:
export class AppError extends Error {
  statusCode: number;
  status: string;
  isOperational: boolean;
  
  constructor(message: string, statusCode: number, status?: string, isOperational = true)
}

export const createError = {
  badRequest: (message: string) => AppError,
  unauthorized: (message: string) => AppError,
  forbidden: (message: string) => AppError,
  notFound: (message: string) => AppError,
  conflict: (message: string) => AppError,
  tooManyRequests: (message: string) => AppError,
  internalServerError: (message: string) => AppError,
}

// Arabic error messages support
export const errorMessages = {
  ar: { /* Arabic error messages */ },
  en: { /* English error messages */ }
}
```

**Implementation Requirements**:
- HTTP status code mapping
- Arabic/English error message support
- Operational vs programming error distinction
- Integration with existing error middleware
- Proper TypeScript typing

#### **1.2 Create Async Handler Utility**
**File**: `apps/api/src/utils/asyncHandler.ts`

```typescript
// Structure Overview:
import { Request, Response, NextFunction } from 'express';

export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Enhanced version with error context
export const asyncHandlerWithContext = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>,
  context?: string
) => {
  // Implementation with error context tracking
};
```

**Implementation Requirements**:
- Proper TypeScript generics
- Error context preservation
- Integration with error middleware
- Performance optimization
- Request/response type safety

#### **1.3 Create JWT Utility**
**File**: `apps/api/src/utils/jwt.ts`

```typescript
// Structure Overview:
import jwt from 'jsonwebtoken';

export interface JWTPayload {
  userId: string;
  email: string;
  role: 'CLIENT' | 'EXPERT' | 'ADMIN';
  iat?: number;
  exp?: number;
}

export const jwtUtils = {
  sign: (payload: JWTPayload, options?: jwt.SignOptions) => string,
  verify: (token: string) => JWTPayload,
  decode: (token: string) => JWTPayload | null,
  refresh: (token: string) => string,
  blacklist: (token: string) => Promise<void>,
  isBlacklisted: (token: string) => Promise<boolean>
}
```

**Implementation Requirements**:
- Access token and refresh token support
- Token blacklisting for logout
- Proper error handling for expired/invalid tokens
- Integration with existing auth middleware
- Environment-based configuration

### **Phase 2: Fix Import Paths and Dependencies (Day 3)**

#### **2.1 Install Missing Dependencies**
```bash
# In packages/database
npm install @supabase/supabase-js

# In apps/api (if needed)
npm install @types/jsonwebtoken
```

#### **2.2 Fix Import Path Corrections**

**File**: `apps/api/src/routes/ai.ts`
```typescript
// Fix imports:
import { validateBody } from '../middleware/validation'; // was validateRequest
import { createError } from '../utils/errors';
import { asyncHandler } from '../utils/asyncHandler';
```

**File**: `apps/api/src/services/ai/enhancedAIService.ts`
```typescript
// Fix import:
import { OpenRouterService } from '../openrouter'; // was './openRouterService'
```

**File**: `apps/api/src/services/ai/smartMatchingService.ts`
```typescript
// Fix import:
import { OpenRouterService } from '../openrouter'; // was '../openRouterService'
```

**File**: `apps/api/src/tests/aiIntegrationTest.ts`
```typescript
// Fix import:
import { OpenRouterService } from '../services/openrouter'; // was '../services/openRouterService'
```

#### **2.3 Update Validation Middleware Export**
**File**: `apps/api/src/middleware/validation.ts`
```typescript
// Add missing export:
export const validateRequest = validateBody; // Alias for backward compatibility
// OR update all usages to use validateBody consistently
```

### **Phase 3: Fix Type Annotations (Day 4)**

#### **3.1 Fix Route Handler Types**
**File**: `apps/api/src/routes/ai.ts`

```typescript
// Fix all route handlers with proper typing:
import { Request, Response } from 'express';

// Example fix for one handler:
asyncHandler(async (req: Request, res: Response) => {
  // Handler implementation
});

// Apply to all 16 handlers with implicit 'any' types
```

#### **3.2 Fix Service Type Issues**
**File**: `apps/api/src/services/aiConversation.ts`

```typescript
// Fix stepKeys.indexOf() type issue:
const stepKeys = Object.keys(conversationSteps) as (keyof typeof conversationSteps)[];
const currentIndex = stepKeys.indexOf(currentStep as keyof typeof conversationSteps);
```

**File**: `apps/api/src/tests/aiIntegrationTest.ts`
```typescript
// Fix array method type issue:
expect(results.every((r: any) => r.length > 0)).toBe(true);
// OR better:
expect(results.every((r: string) => r.length > 0)).toBe(true);
```

#### **3.3 Fix Supabase Types**
**File**: `packages/database/src/supabase.ts`
```typescript
// Fix callback parameter type:
}, (payload: any) => {
// OR better with proper typing:
}, (payload: RealtimePostgresChangesPayload<any>) => {
```

### **Phase 4: Service Integration Repair (Day 5)**

#### **4.1 Validate OpenRouter Service Integration**
- Ensure OpenRouterService class is properly exported
- Verify API key configuration
- Test service instantiation
- Validate method signatures

#### **4.2 Test API Endpoints**
- Run TypeScript compilation
- Test each AI route endpoint
- Validate error handling
- Check service integrations

#### **4.3 Integration Testing**
```bash
# Run tests to validate fixes:
cd apps/api
npm run type-check  # Should show 0 errors
npm test           # Should pass all tests
npm run build      # Should compile successfully
```

---

## 📋 **DETAILED FILE SPECIFICATIONS**

### **Error Utility Implementation**
**File**: `apps/api/src/utils/errors.ts`
- **Size**: ~150 lines
- **Dependencies**: None
- **Exports**: AppError class, createError object, errorMessages
- **Features**: HTTP status mapping, i18n support, operational error detection

### **Async Handler Implementation**
**File**: `apps/api/src/utils/asyncHandler.ts`
- **Size**: ~50 lines
- **Dependencies**: express types
- **Exports**: asyncHandler function, asyncHandlerWithContext
- **Features**: Promise error catching, context preservation

### **JWT Utility Implementation**
**File**: `apps/api/src/utils/jwt.ts`
- **Size**: ~200 lines
- **Dependencies**: jsonwebtoken, redis (for blacklisting)
- **Exports**: jwtUtils object with sign/verify/refresh methods
- **Features**: Token management, blacklisting, refresh logic

---

## 🧪 **TESTING STRATEGY**

### **Unit Tests Required**
1. **Error Utility Tests**: Error creation, status codes, i18n
2. **Async Handler Tests**: Error catching, context preservation
3. **JWT Utility Tests**: Token operations, security validation

### **Integration Tests Required**
1. **API Route Tests**: All AI endpoints functionality
2. **Service Integration Tests**: OpenRouter, Smart Matching
3. **Error Handling Tests**: Proper error propagation

### **Validation Checklist**
- [ ] TypeScript compilation: 0 errors
- [ ] All imports resolve correctly
- [ ] All services instantiate properly
- [ ] Error handling works end-to-end
- [ ] JWT operations function correctly
- [ ] API endpoints respond properly

---

## ⚠️ **RISK MITIGATION**

### **High Risk Areas**
1. **Cascading Errors**: Fixing one import might break others
   - **Mitigation**: Fix imports incrementally, test after each change

2. **Service Dependencies**: OpenRouter service integration complexity
   - **Mitigation**: Create mock service for testing if needed

3. **Type System Changes**: Strict typing might reveal hidden issues
   - **Mitigation**: Use gradual typing approach, fix critical paths first

### **Rollback Strategy**
- Commit each utility file creation separately
- Test compilation after each major change
- Keep backup of working state before changes
- Document all changes for easy reversal

---

## 📊 **SUCCESS CRITERIA**

### **Phase Completion Metrics**
- [ ] **0 TypeScript compilation errors**
- [ ] **All utility files created and tested**
- [ ] **All import paths corrected**
- [ ] **All services integrate properly**
- [ ] **API endpoints respond correctly**

### **Quality Gates**
- [ ] **Code Review**: All utility implementations reviewed
- [ ] **Security Review**: JWT and error handling security validated
- [ ] **Performance Test**: No significant performance degradation
- [ ] **Documentation**: All new utilities documented

---

## 🚀 **NEXT STEPS AFTER COMPLETION**

1. **Proceed to Mobile UI Implementation**: Phase 2 of master plan
2. **Update Documentation**: Reflect new utility structure
3. **Team Training**: Brief team on new error handling patterns
4. **Monitoring Setup**: Add logging for new error handling

**🎯 CRITICAL SUCCESS FACTOR**: All 29 TypeScript errors must be resolved before proceeding to UI development to avoid blocking mobile team progress.

---

## 📝 **DETAILED ERROR RESOLUTION GUIDE**

### **Error Category 1: Missing Utility Imports (22 errors)**

#### **validateRequest Import Error (1 error)**
**File**: `apps/api/src/routes/ai.ts:9`
```typescript
// Current (broken):
import { validateRequest } from '../middleware/validation';

// Fix Option 1 - Add export to validation middleware:
// In apps/api/src/middleware/validation.ts:
export const validateRequest = validateBody; // Add this line

// Fix Option 2 - Update import to use existing export:
import { validateBody as validateRequest } from '../middleware/validation';
```

#### **createError Import Errors (4 errors)**
**Files**:
- `apps/api/src/routes/ai.ts:14`
- `apps/api/src/services/aiConversation.ts:8`
- `apps/api/src/services/openrouter.ts:8`

**Solution**: Create comprehensive error utility
```typescript
// File: apps/api/src/utils/errors.ts
import { Request } from 'express';

export class AppError extends Error {
  public readonly statusCode: number;
  public readonly status: string;
  public readonly isOperational: boolean;
  public readonly timestamp: string;
  public readonly path?: string;
  public readonly method?: string;

  constructor(
    message: string,
    statusCode: number,
    status?: string,
    isOperational = true,
    req?: Request
  ) {
    super(message);

    this.statusCode = statusCode;
    this.status = status || (statusCode >= 400 && statusCode < 500 ? 'fail' : 'error');
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();

    if (req) {
      this.path = req.path;
      this.method = req.method;
    }

    Error.captureStackTrace(this, this.constructor);
  }
}

// HTTP error creators with Arabic support
export const createError = {
  badRequest: (message: string, req?: Request) =>
    new AppError(message, 400, 'fail', true, req),

  unauthorized: (message: string, req?: Request) =>
    new AppError(message, 401, 'fail', true, req),

  forbidden: (message: string, req?: Request) =>
    new AppError(message, 403, 'fail', true, req),

  notFound: (message: string, req?: Request) =>
    new AppError(message, 404, 'fail', true, req),

  conflict: (message: string, req?: Request) =>
    new AppError(message, 409, 'fail', true, req),

  tooManyRequests: (message: string, req?: Request) =>
    new AppError(message, 429, 'fail', true, req),

  internalServerError: (message: string, req?: Request) =>
    new AppError(message, 500, 'error', true, req),
};

// Localized error messages
export const errorMessages = {
  ar: {
    VALIDATION_ERROR: 'خطأ في التحقق من البيانات',
    UNAUTHORIZED: 'غير مخول للوصول',
    NOT_FOUND: 'المورد غير موجود',
    INTERNAL_ERROR: 'خطأ داخلي في الخادم',
    RATE_LIMIT: 'تم تجاوز حد الطلبات',
    AI_SERVICE_ERROR: 'خطأ في خدمة الذكاء الاصطناعي',
    INVALID_INPUT: 'بيانات الدخل غير صحيحة',
  },
  en: {
    VALIDATION_ERROR: 'Validation error',
    UNAUTHORIZED: 'Unauthorized access',
    NOT_FOUND: 'Resource not found',
    INTERNAL_ERROR: 'Internal server error',
    RATE_LIMIT: 'Rate limit exceeded',
    AI_SERVICE_ERROR: 'AI service error',
    INVALID_INPUT: 'Invalid input data',
  },
};

// Error message helper
export const getErrorMessage = (key: keyof typeof errorMessages.ar, language: 'ar' | 'en' = 'ar') => {
  return errorMessages[language][key] || errorMessages.en[key];
};
```

#### **asyncHandler Import Error (1 error)**
**File**: `apps/api/src/routes/ai.ts:15`

**Solution**: Create robust async handler
```typescript
// File: apps/api/src/utils/asyncHandler.ts
import { Request, Response, NextFunction } from 'express';
import { AppError } from './errors';

type AsyncFunction = (req: Request, res: Response, next: NextFunction) => Promise<any>;

export const asyncHandler = (fn: AsyncFunction) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch((error: Error) => {
      // Add request context to error
      if (error instanceof AppError) {
        error.path = req.path;
        error.method = req.method;
      }
      next(error);
    });
  };
};

// Enhanced version with timeout and context
export const asyncHandlerWithTimeout = (fn: AsyncFunction, timeoutMs = 30000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new AppError('Request timeout', 408, 'fail', true, req));
      }, timeoutMs);
    });

    Promise.race([
      Promise.resolve(fn(req, res, next)),
      timeoutPromise
    ]).catch((error: Error) => {
      if (error instanceof AppError) {
        error.path = req.path;
        error.method = req.method;
      }
      next(error);
    });
  };
};

// Specialized handler for AI operations
export const aiAsyncHandler = (fn: AsyncFunction) => {
  return asyncHandlerWithTimeout(fn, 60000); // 60s timeout for AI operations
};
```

#### **jwtUtils Import Error (1 error)**
**File**: `apps/api/src/services/websocket.ts:10`

**Solution**: Create comprehensive JWT utility
```typescript
// File: apps/api/src/utils/jwt.ts
import jwt from 'jsonwebtoken';
import { createClient } from 'redis';
import { AppError } from './errors';

export interface JWTPayload {
  userId: string;
  email: string;
  role: 'CLIENT' | 'EXPERT' | 'ADMIN';
  sessionId?: string;
  iat?: number;
  exp?: number;
}

export interface JWTOptions {
  expiresIn?: string | number;
  audience?: string;
  issuer?: string;
}

class JWTUtils {
  private redis = createClient({
    url: process.env.REDIS_URL || 'redis://localhost:6379'
  });

  private accessTokenSecret = process.env.JWT_ACCESS_SECRET!;
  private refreshTokenSecret = process.env.JWT_REFRESH_SECRET!;

  constructor() {
    this.redis.connect().catch(console.error);
  }

  // Sign access token
  sign(payload: Omit<JWTPayload, 'iat' | 'exp'>, options: JWTOptions = {}): string {
    const defaultOptions: jwt.SignOptions = {
      expiresIn: '15m',
      issuer: 'freela-syria',
      audience: 'freela-users',
      ...options,
    };

    return jwt.sign(payload, this.accessTokenSecret, defaultOptions);
  }

  // Sign refresh token
  signRefresh(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    return jwt.sign(payload, this.refreshTokenSecret, {
      expiresIn: '7d',
      issuer: 'freela-syria',
      audience: 'freela-users',
    });
  }

  // Verify access token
  verify(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.accessTokenSecret) as JWTPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AppError('Token expired', 401);
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AppError('Invalid token', 401);
      }
      throw new AppError('Token verification failed', 401);
    }
  }

  // Verify refresh token
  verifyRefresh(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.refreshTokenSecret) as JWTPayload;
    } catch (error) {
      throw new AppError('Invalid refresh token', 401);
    }
  }

  // Decode without verification
  decode(token: string): JWTPayload | null {
    try {
      return jwt.decode(token) as JWTPayload;
    } catch {
      return null;
    }
  }

  // Refresh access token
  async refresh(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    const payload = this.verifyRefresh(refreshToken);

    // Check if refresh token is blacklisted
    const isBlacklisted = await this.isBlacklisted(refreshToken);
    if (isBlacklisted) {
      throw new AppError('Refresh token is invalid', 401);
    }

    // Generate new tokens
    const newAccessToken = this.sign({
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
    });

    const newRefreshToken = this.signRefresh({
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
    });

    // Blacklist old refresh token
    await this.blacklist(refreshToken);

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    };
  }

  // Blacklist token (for logout)
  async blacklist(token: string): Promise<void> {
    try {
      const decoded = this.decode(token);
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await this.redis.setEx(`blacklist:${token}`, ttl, 'true');
        }
      }
    } catch (error) {
      console.error('Error blacklisting token:', error);
    }
  }

  // Check if token is blacklisted
  async isBlacklisted(token: string): Promise<boolean> {
    try {
      const result = await this.redis.get(`blacklist:${token}`);
      return result === 'true';
    } catch (error) {
      console.error('Error checking blacklist:', error);
      return false;
    }
  }

  // Extract token from Authorization header
  extractFromHeader(authHeader?: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }
}

export const jwtUtils = new JWTUtils();
```

### **Error Category 2: Import Path Corrections (3 errors)**

#### **OpenRouter Service Import Errors**
**Files**:
- `apps/api/src/services/ai/enhancedAIService.ts:5`
- `apps/api/src/services/ai/smartMatchingService.ts:7`
- `apps/api/src/tests/aiIntegrationTest.ts:7`

**Current Issues**:
```typescript
// Wrong paths:
import { OpenRouterService } from './openRouterService';     // enhancedAIService.ts
import { OpenRouterService } from '../openRouterService';    // smartMatchingService.ts
import { OpenRouterService } from '../services/openRouterService'; // aiIntegrationTest.ts
```

**Solutions**:
```typescript
// Correct paths (assuming openrouter.ts exists):
import { OpenRouterService } from '../openrouter';           // enhancedAIService.ts
import { OpenRouterService } from '../openrouter';           // smartMatchingService.ts
import { OpenRouterService } from '../services/openrouter';  // aiIntegrationTest.ts

// OR if we need to create openRouterService.ts:
// Create apps/api/src/services/openRouterService.ts as alias:
export { OpenRouterService } from './openrouter';
```

### **Error Category 3: Type Annotation Fixes (16 errors)**

#### **Route Handler Parameter Types**
**File**: `apps/api/src/routes/ai.ts` (16 implicit 'any' errors)

**Current Issue**:
```typescript
asyncHandler(async (req, res) => {  // req and res have implicit 'any' type
```

**Solution**:
```typescript
import { Request, Response } from 'express';

// Fix all route handlers:
asyncHandler(async (req: Request, res: Response) => {
  // Handler implementation
});

// For handlers with custom request types:
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: 'CLIENT' | 'EXPERT' | 'ADMIN';
  };
}

asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // Handler with authenticated user
});
```

### **Error Category 4: Dependency Issues (2 errors)**

#### **Supabase Dependency Missing**
**File**: `packages/database/src/supabase.ts:4`

**Solution**:
```bash
cd packages/database
npm install @supabase/supabase-js
npm install --save-dev @types/node
```

#### **Supabase Callback Type**
**File**: `packages/database/src/supabase.ts:74`

**Current Issue**:
```typescript
}, (payload) => {  // payload has implicit 'any' type
```

**Solution**:
```typescript
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';

}, (payload: RealtimePostgresChangesPayload<any>) => {
  // Properly typed callback
});
```

---

## 🔧 **IMPLEMENTATION CHECKLIST**

### **Day 1: Utility Files Creation**
- [ ] Create `apps/api/src/utils/errors.ts` (150 lines)
- [ ] Create `apps/api/src/utils/asyncHandler.ts` (50 lines)
- [ ] Create `apps/api/src/utils/jwt.ts` (200 lines)
- [ ] Test utility imports in isolation
- [ ] Validate TypeScript compilation for utilities

### **Day 2: Import Path Fixes**
- [ ] Fix validateRequest import in ai.ts
- [ ] Update all createError imports (4 files)
- [ ] Fix asyncHandler import in ai.ts
- [ ] Fix jwtUtils import in websocket.ts
- [ ] Fix OpenRouter service imports (3 files)
- [ ] Test compilation after each fix

### **Day 3: Type Annotation Fixes**
- [ ] Add Request/Response types to all route handlers (16 fixes)
- [ ] Fix stepKeys.indexOf() type issue
- [ ] Fix array method type in tests
- [ ] Add Supabase callback types
- [ ] Validate all type fixes

### **Day 4: Dependency Installation**
- [ ] Install @supabase/supabase-js in packages/database
- [ ] Install any missing type definitions
- [ ] Update package-lock.json files
- [ ] Test dependency resolution

### **Day 5: Integration Testing**
- [ ] Run full TypeScript compilation
- [ ] Test API server startup
- [ ] Validate all service integrations
- [ ] Run existing test suite
- [ ] Performance validation

---

## 📊 **VALIDATION COMMANDS**

```bash
# TypeScript compilation check
cd apps/api && npm run type-check

# Build validation
cd apps/api && npm run build

# Test suite validation
cd apps/api && npm test

# Service integration test
cd apps/api && npm run dev  # Should start without errors

# Dependency audit
cd apps/api && npm audit
cd packages/database && npm audit
```

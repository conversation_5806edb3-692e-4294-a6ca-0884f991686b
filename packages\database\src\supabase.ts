// Supabase Client Configuration for Freela Syria
// This replaces the Prisma client with Supabase client

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from './types/supabase';

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL || 'https://bivignfixaqrmdcbsnqh.supabase.co';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdmlnbmZpeGFxcm1kY2JzbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MzY1MDYsImV4cCI6MjA2NTQxMjUwNn0.cMwSd8oFF5CDyXBaaqPL7EVHhF9l32ERd6krX4DAo4E';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdmlnbmZpeGFxcm1kY2JzbnFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTgzNjUwNiwiZXhwIjoyMDY1NDEyNTA2fQ.Ue6KVdG7c-iwZWKr4D-BhRzj82yp2b81uikFYXSdvZ8';

// Create Supabase clients
export const supabase: SupabaseClient<Database> = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Service role client for admin operations (server-side only)
export const supabaseAdmin: SupabaseClient<Database> = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Real-time AI chat subscription helper
export class SupabaseAIChat {
  private sessionId: string;
  private subscription: any;

  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }

  // Send a new AI message
  async sendMessage(content: string, role: 'user' | 'assistant', messageType: string = 'text') {
    const { data, error } = await supabase
      .from('ai_conversation_messages')
      .insert({
        session_id: this.sessionId,
        role,
        content,
        message_type: messageType,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error sending AI message:', error);
      throw error;
    }

    return data;
  }

  // Subscribe to real-time messages
  subscribeToMessages(callback: (message: any) => void) {
    this.subscription = supabase
      .channel(`ai-chat-${this.sessionId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'ai_conversation_messages',
        filter: `session_id=eq.${this.sessionId}`
      }, (payload: any) => {
        callback(payload.new);
      })
      .subscribe();

    return this.subscription;
  }

  // Unsubscribe from messages
  unsubscribe() {
    if (this.subscription) {
      supabase.removeChannel(this.subscription);
    }
  }

  // Get conversation history
  async getMessages(limit: number = 50) {
    const { data, error } = await supabase
      .from('ai_conversation_messages')
      .select('*')
      .eq('session_id', this.sessionId)
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }

    return data;
  }

  // Update session status
  async updateSession(updates: any) {
    const { data, error } = await supabase
      .from('ai_conversation_sessions')
      .update({
        ...updates,
        last_active_at: new Date().toISOString()
      })
      .eq('id', this.sessionId)
      .select()
      .single();

    if (error) {
      console.error('Error updating session:', error);
      throw error;
    }

    return data;
  }
}

// User management helpers
export class SupabaseUserService {
  // Create a new user with profile
  static async createUser(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role: 'CLIENT' | 'EXPERT' | 'ADMIN';
    language?: string;
  }) {
    // Sign up the user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          first_name: userData.firstName,
          last_name: userData.lastName,
          role: userData.role,
          language: userData.language || 'ar'
        }
      }
    });

    if (authError) {
      console.error('Error creating user:', authError);
      throw authError;
    }

    return authData;
  }

  // Sign in with Google OAuth
  static async signInWithGoogle() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    });

    if (error) {
      console.error('Error signing in with Google:', error);
      throw error;
    }

    return data;
  }

  // Get current user profile
  static async getCurrentUserProfile() {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return null;

    const { data: profile, error } = await supabase
      .from('users')
      .select(`
        *,
        expert_profiles(*),
        client_profiles(*)
      `)
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }

    return profile;
  }

  // Update user profile
  static async updateUserProfile(userId: string, updates: any) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }

    return data;
  }
}

// Expert profile helpers
export class SupabaseExpertService {
  // Get expert profile with services
  static async getExpertProfile(userId: string) {
    const { data, error } = await supabase
      .from('expert_profiles')
      .select(`
        *,
        users(*),
        services(*)
      `)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching expert profile:', error);
      throw error;
    }

    return data;
  }

  // Update expert profile
  static async updateExpertProfile(userId: string, updates: any) {
    const { data, error } = await supabase
      .from('expert_profiles')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating expert profile:', error);
      throw error;
    }

    return data;
  }

  // Create a new service
  static async createService(expertId: string, serviceData: any) {
    const { data, error } = await supabase
      .from('services')
      .insert({
        expert_id: expertId,
        ...serviceData
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating service:', error);
      throw error;
    }

    return data;
  }
}

// AI onboarding helpers
export class SupabaseAIService {
  // Start a new AI conversation session
  static async startConversation(userId: string, sessionType: string, userRole: 'CLIENT' | 'EXPERT') {
    const { data, error } = await supabase
      .from('ai_conversation_sessions')
      .insert({
        user_id: userId,
        session_type: sessionType,
        user_role: userRole,
        current_step: 'welcome',
        status: 'active'
      })
      .select()
      .single();

    if (error) {
      console.error('Error starting AI conversation:', error);
      throw error;
    }

    return data;
  }

  // Get user's AI sessions
  static async getUserSessions(userId: string) {
    const { data, error } = await supabase
      .from('ai_conversation_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching AI sessions:', error);
      throw error;
    }

    return data;
  }

  // Create AI recommendation
  static async createRecommendation(recommendationData: any) {
    const { data, error } = await supabase
      .from('ai_recommendations')
      .insert(recommendationData)
      .select()
      .single();

    if (error) {
      console.error('Error creating AI recommendation:', error);
      throw error;
    }

    return data;
  }

  // Get user recommendations
  static async getUserRecommendations(userId: string) {
    const { data, error } = await supabase
      .from('ai_recommendations')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'pending')
      .order('priority', { ascending: false });

    if (error) {
      console.error('Error fetching recommendations:', error);
      throw error;
    }

    return data;
  }
}

export default supabase;

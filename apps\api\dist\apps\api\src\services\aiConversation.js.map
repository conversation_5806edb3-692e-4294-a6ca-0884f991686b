{"version": 3, "file": "aiConversation.js", "sourceRoot": "", "sources": ["../../../../../src/services/aiConversation.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,6CAA+F;AAC/F,4CAAyC;AACzC,4CAA8C;AAC9C,mCAAgC;AAEhC,mDAAmD;AACtC,QAAA,kBAAkB,GAAG;IAChC,MAAM,EAAE;QACN,OAAO,EAAE,SAAS;QAClB,YAAY,EAAE,cAAc;QAC5B,eAAe,EAAE,iBAAiB;QAClC,eAAe,EAAE,iBAAiB;QAClC,YAAY,EAAE,cAAc;QAC5B,WAAW,EAAE,aAAa;QAC1B,OAAO,EAAE,SAAS;QAClB,UAAU,EAAE,YAAY;KACzB;IACD,MAAM,EAAE;QACN,OAAO,EAAE,SAAS;QAClB,iBAAiB,EAAE,mBAAmB;QACtC,gBAAgB,EAAE,kBAAkB;QACpC,gBAAgB,EAAE,kBAAkB;QACpC,SAAS,EAAE,WAAW;QACtB,YAAY,EAAE,cAAc;QAC5B,kBAAkB,EAAE,oBAAoB;QACxC,OAAO,EAAE,SAAS;QAClB,UAAU,EAAE,YAAY;KACzB;CACO,CAAC;AA6BX,MAAM,qBAAqB;IACjB,QAAQ,GAAqC,IAAI,GAAG,EAAE,CAAC;IAE/D;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,MAKvB;QACC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,GAAG,YAAY,EAAE,GAAG,MAAM,CAAC;QAE1E,MAAM,SAAS,GAAG,IAAA,eAAM,GAAE,CAAC;QAC3B,MAAM,OAAO,GAAwB;YACnC,EAAE,EAAE,SAAS;YACb,MAAM;YACN,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,WAAW,EAAE,0BAAkB,CAAC,QAAQ,CAAC,CAAC,OAAO;YACjD,MAAM,EAAE,QAAQ;YAChB,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,EAAE;YACjB,eAAe,EAAE,EAAE;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;QAEF,4EAA4E;QAC5E,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEtC,2BAA2B;QAC3B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAClE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEtC,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;YAC7C,SAAS;YACT,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,WAAW;SACZ,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,SAAiB,EACjB,WAAmB;QAEnB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,oBAAW,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAChC,MAAM,oBAAW,CAAC,UAAU,CAAC,oCAAoC,CAAC,CAAC;QACrE,CAAC;QAED,8BAA8B;QAC9B,MAAM,eAAe,GAAgB;YACnC,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QACF,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEvC,6BAA6B;QAC7B,MAAM,OAAO,GAAwB;YACnC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,aAAa,EAAE,OAAO,CAAC,aAAa;SACrC,CAAC;QAEF,IAAI,CAAC;YACH,kBAAkB;YAClB,MAAM,UAAU,GAAG,MAAM,8BAAiB,CAAC,cAAc,CACvD,OAAO,CAAC,QAAQ,EAChB,OAAO,EACP;gBACE,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,GAAG;aACf,CACF,CAAC;YAEF,oBAAoB;YACpB,MAAM,aAAa,GAAgB;gBACjC,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,6BAA6B;YAC7B,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrC,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAElC,iCAAiC;YACjC,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YAE5E,sBAAsB;YACtB,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE3C,4BAA4B;YAC5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEtC,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,SAAS;gBACT,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,MAAM;aACtC,CAAC,CAAC;YAEH,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;QAEhD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC1C,SAAS;gBACT,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,oBAAW,CAAC,mBAAmB,CAAC,2BAA2B,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAc;QAC5B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAC9C,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,CACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,OAA4B;QAC/D,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAEvC,MAAM,cAAc,GAAG;YACrB,EAAE,EAAE;gBACF,MAAM,EAAE;;;;;;;;;;;+BAWe;gBAEvB,MAAM,EAAE;;;;;;;;;;;;4CAY4B;aACrC;YACD,EAAE,EAAE;gBACF,MAAM,EAAE;;;;;;;;;;;+CAW+B;gBAEvC,MAAM,EAAE;;;;;;;;;;;;+DAY+C;aACxD;SACF,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;YAC3C,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,OAA4B,EAC5B,WAAmB,EACnB,UAAkB;QAElB,sFAAsF;QACtF,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAE1C,IAAI,CAAC;YACH,mDAAmD;YACnD,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;YAClE,CAAC;iBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACjD,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,WAAW;gBACX,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,OAA4B,EAC5B,WAAmB,EACnB,WAAmB;QAEnB,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE5C,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,0BAAkB,CAAC,MAAM,CAAC,YAAY;gBACzC,oCAAoC;gBACpC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBACjE,MAAM;YAER,KAAK,0BAAkB,CAAC,MAAM,CAAC,eAAe;gBAC5C,0CAA0C;gBAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACvD,IAAI,UAAU,EAAE,CAAC;oBACf,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC;gBACpC,CAAC;gBACD,MAAM;YAER,KAAK,0BAAkB,CAAC,MAAM,CAAC,YAAY;gBACzC,+BAA+B;gBAC/B,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;oBAChC,aAAa,CAAC,YAAY,GAAG,EAAE,CAAC;gBAClC,CAAC;gBACD,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC7C,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,OAA4B,EAC5B,WAAmB,EACnB,WAAmB;QAEnB,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE5C,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,0BAAkB,CAAC,MAAM,CAAC,iBAAiB;gBAC9C,gCAAgC;gBAChC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;gBACvD,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAC/D,MAAM;YAER,KAAK,0BAAkB,CAAC,MAAM,CAAC,gBAAgB;gBAC7C,4BAA4B;gBAC5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC5B,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAC;gBAC9B,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzC,MAAM;YAER,KAAK,0BAAkB,CAAC,MAAM,CAAC,gBAAgB;gBAC7C,8BAA8B;gBAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBACzD,IAAI,WAAW,EAAE,CAAC;oBAChB,aAAa,CAAC,OAAO,GAAG,WAAW,CAAC;gBACtC,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,OAA4B;QAC/D,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAC1C,MAAM,KAAK,GAAG,0BAAkB,CAAC,QAAQ,CAAC,CAAC;QAC3C,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACtC,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAEnD,kDAAkD;QAClD,IAAI,YAAY,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,yBAAyB;YACzB,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,2DAA2D;IACnD,kBAAkB,CAAC,OAAe;QACxC,sEAAsE;QACtE,MAAM,QAAQ,GAAG;YACf,OAAO,EAAE,QAAQ;YACjB,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,SAAS;YAClB,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE,WAAW;SACrB,CAAC;QAEF,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzD,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxE,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,iBAAiB,CAAC,OAAe;QACvC,yCAAyC;QACzC,MAAM,WAAW,GAAG,gCAAgC,CAAC;QACrD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACzC,IAAI,KAAK,EAAE,CAAC;YACV,OAAO;gBACL,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,QAAQ,EAAE,KAAK;aAChB,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,aAAa,CAAC,OAAe;QACnC,8BAA8B;QAC9B,MAAM,YAAY,GAAG;YACnB,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;YAC/D,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW;SACrE,CAAC;QAEF,OAAO,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACjC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CACtC,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,OAAe;QACvC,8BAA8B;QAC9B,MAAM,QAAQ,GAAG,iCAAiC,CAAC;QACnD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,KAAK,EAAE,CAAC;YACV,OAAO;gBACL,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzB,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACnD,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACxC,8BAA8B;QAC9B,MAAM,WAAW,GAAG,0CAA0C,CAAC;QAC/D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACzC,IAAI,KAAK,EAAE,CAAC;YACV,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxB,QAAQ,EAAE,KAAK;aAChB,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,KAAa;QACtC,IAAI,KAAK,GAAG,CAAC;YAAE,OAAO,UAAU,CAAC;QACjC,IAAI,KAAK,GAAG,CAAC;YAAE,OAAO,cAAc,CAAC;QACrC,IAAI,KAAK,GAAG,CAAC;YAAE,OAAO,UAAU,CAAC;QACjC,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED,4BAA4B;AACf,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC"}
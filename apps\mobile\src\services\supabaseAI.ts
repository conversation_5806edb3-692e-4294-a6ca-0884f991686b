// Mobile App Supabase AI Integration
// Real-time AI chat with enhanced features for React Native

import { supabase, SupabaseAIChat, SupabaseUserService, SupabaseAIService } from '@freela/database/src/supabase';
import { Alert } from 'react-native';

export interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  confidence?: number;
  extractedData?: any;
}

export interface AISession {
  id: string;
  currentStep: string;
  status: string;
  userRole: 'CLIENT' | 'EXPERT';
  messages: AIMessage[];
  extractedData: any;
  recommendations: any[];
}

export class MobileAIService {
  private currentSession: AISession | null = null;
  private aiChat: SupabaseAIChat | null = null;
  private messageSubscription: any = null;

  /**
   * Start a new AI onboarding session
   */
  async startOnboardingSession(userRole: 'CLIENT' | 'EXPERT'): Promise<AISession> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Start AI session
      const session = await SupabaseAIService.startConversation(
        user.id,
        'onboarding',
        userRole
      );

      // Initialize AI chat
      this.aiChat = new SupabaseAIChat(session.id);

      // Get initial messages
      const messages = await this.aiChat.getMessages();

      this.currentSession = {
        id: session.id,
        currentStep: session.current_step || 'welcome',
        status: session.status,
        userRole: session.user_role,
        messages: messages.map(msg => ({
          id: msg.id,
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: msg.created_at,
          confidence: msg.confidence || undefined,
        })),
        extractedData: session.extracted_data || {},
        recommendations: session.recommendations || [],
      };

      // Subscribe to real-time messages
      this.subscribeToMessages();

      return this.currentSession;
    } catch (error) {
      console.error('Error starting AI session:', error);
      Alert.alert('خطأ', 'فشل في بدء جلسة الذكاء الاصطناعي');
      throw error;
    }
  }

  /**
   * Send a message to the AI
   */
  async sendMessage(content: string): Promise<void> {
    if (!this.aiChat || !this.currentSession) {
      throw new Error('No active AI session');
    }

    try {
      // Send user message
      await this.aiChat.sendMessage(content, 'user');

      // Call enhanced AI API for processing
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/api/ai/enhanced/conversation/${this.currentSession.id}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
        },
        body: JSON.stringify({
          message: content,
          currentStep: this.currentSession.currentStep,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to process message');
      }

      const result = await response.json();

      // Update session state
      this.currentSession.currentStep = result.data.nextStep;
      this.currentSession.extractedData = {
        ...this.currentSession.extractedData,
        ...result.data.extractedData,
      };
      this.currentSession.recommendations = result.data.recommendations || [];

    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('خطأ', 'فشل في إرسال الرسالة');
      throw error;
    }
  }

  /**
   * Subscribe to real-time messages
   */
  private subscribeToMessages() {
    if (!this.aiChat || !this.currentSession) return;

    this.messageSubscription = this.aiChat.subscribeToMessages((message: any) => {
      if (this.currentSession) {
        const newMessage: AIMessage = {
          id: message.id,
          role: message.role,
          content: message.content,
          timestamp: message.created_at,
          confidence: message.confidence,
        };

        this.currentSession.messages.push(newMessage);
        
        // Notify listeners (you can implement event emitter here)
        this.notifyMessageReceived(newMessage);
      }
    });
  }

  /**
   * Get current session
   */
  getCurrentSession(): AISession | null {
    return this.currentSession;
  }

  /**
   * Get session messages
   */
  getMessages(): AIMessage[] {
    return this.currentSession?.messages || [];
  }

  /**
   * Get extracted data
   */
  getExtractedData(): any {
    return this.currentSession?.extractedData || {};
  }

  /**
   * Get AI recommendations
   */
  getRecommendations(): any[] {
    return this.currentSession?.recommendations || [];
  }

  /**
   * Generate expert profile from session data
   */
  async generateExpertProfile(): Promise<any> {
    if (!this.currentSession || this.currentSession.userRole !== 'EXPERT') {
      throw new Error('Invalid session for profile generation');
    }

    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/api/ai/enhanced/profile/generate/${this.currentSession.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to generate profile');
      }

      const result = await response.json();
      return result.data.profile;
    } catch (error) {
      console.error('Error generating profile:', error);
      Alert.alert('خطأ', 'فشل في إنشاء الملف الشخصي');
      throw error;
    }
  }

  /**
   * Complete the onboarding session
   */
  async completeSession(): Promise<void> {
    if (!this.aiChat || !this.currentSession) return;

    try {
      await this.aiChat.updateSession({
        status: 'completed',
        completed_at: new Date().toISOString(),
      });

      this.currentSession.status = 'completed';
    } catch (error) {
      console.error('Error completing session:', error);
    }
  }

  /**
   * Clean up resources
   */
  cleanup() {
    if (this.messageSubscription) {
      this.messageSubscription.unsubscribe();
      this.messageSubscription = null;
    }

    if (this.aiChat) {
      this.aiChat.unsubscribe();
      this.aiChat = null;
    }

    this.currentSession = null;
  }

  /**
   * Notify message received (implement your event system here)
   */
  private notifyMessageReceived(message: AIMessage) {
    // You can implement React Context, Redux, or other state management here
    console.log('New AI message received:', message);
  }
}

// Google OAuth integration for mobile
export class MobileAuthService {
  /**
   * Sign in with Google OAuth
   */
  static async signInWithGoogle(): Promise<any> {
    try {
      // This will redirect to Google OAuth
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'com.freelasyria.app://auth/callback',
        },
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Google sign-in error:', error);
      Alert.alert('خطأ', 'فشل في تسجيل الدخول بجوجل');
      throw error;
    }
  }

  /**
   * Sign out
   */
  static async signOut(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Sign out error:', error);
      Alert.alert('خطأ', 'فشل في تسجيل الخروج');
      throw error;
    }
  }

  /**
   * Get current user profile
   */
  static async getCurrentUser(): Promise<any> {
    try {
      return await SupabaseUserService.getCurrentUserProfile();
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }
}

}

// Enhanced AI Chat Interface with Voice and Image Support
export class EnhancedAIChatService extends MobileAIService {
  /**
   * Send voice message with transcription
   */
  async sendVoiceMessage(audioUri: string, sessionId: string): Promise<AIMessage> {
    try {
      // Import voice recognition service
      const { voiceRecognitionService } = await import('./voiceRecognitionService');

      // Transcribe audio
      const transcription = await voiceRecognitionService.transcribeAudio(audioUri, {
        enableDialectDetection: true,
        enableDataExtraction: true,
      });

      // Send transcribed message
      const message = await this.sendMessage(transcription.transcript, sessionId);

      // Add voice metadata
      return {
        ...message,
        metadata: {
          type: 'voice',
          confidence: transcription.confidence,
          dialect: transcription.dialect,
          extractedData: transcription.extractedData,
        },
      };

    } catch (error) {
      console.error('Error sending voice message:', error);
      Alert.alert('خطأ', 'فشل في إرسال الرسالة الصوتية');
      throw error;
    }
  }

  /**
   * Send image message with analysis
   */
  async sendImageMessage(imageUri: string, sessionId: string, caption?: string): Promise<AIMessage> {
    try {
      // Import image analysis service
      const { imageAnalysisService } = await import('./imageAnalysisService');

      // Analyze image
      const analysis = await imageAnalysisService.analyzePortfolioImage(imageUri);

      // Create message content
      const messageContent = caption
        ? `${caption}\n\n[صورة مرفقة: ${analysis.description}]`
        : `[صورة مرفقة: ${analysis.description}]`;

      // Send message with image analysis
      const message = await this.sendMessage(messageContent, sessionId);

      // Add image metadata
      return {
        ...message,
        metadata: {
          type: 'image',
          imageUri,
          analysis,
        },
      };

    } catch (error) {
      console.error('Error sending image message:', error);
      Alert.alert('خطأ', 'فشل في إرسال الصورة');
      throw error;
    }
  }

  /**
   * Get AI-powered service recommendations
   */
  async getServiceRecommendations(sessionId: string): Promise<any[]> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) throw new Error('Session not found');

      // Extract user data from conversation
      const extractedData = session.extractedData || {};

      // Use smart matching service for recommendations
      const matchingCriteria = {
        projectDescription: extractedData.projectDescription || 'مشروع عام',
        budget: extractedData.budget || { min: 50, max: 200 },
        timeline: extractedData.timeline || 'أسبوع',
        requiredSkills: extractedData.skills || [],
        experienceLevel: extractedData.experienceLevel || 'intermediate',
        projectComplexity: extractedData.complexity || 'medium',
      };

      // This would call the smart matching service
      // For now, return mock recommendations
      return [
        {
          id: '1',
          title: 'تصميم شعار احترافي',
          price: 75,
          expert: 'أحمد محمد',
          rating: 4.8,
          matchScore: 0.95,
        },
        {
          id: '2',
          title: 'تطوير موقع ويب',
          price: 150,
          expert: 'فاطمة أحمد',
          rating: 4.9,
          matchScore: 0.87,
        },
      ];

    } catch (error) {
      console.error('Error getting service recommendations:', error);
      return [];
    }
  }

  /**
   * Generate portfolio summary from images
   */
  async generatePortfolioSummary(imageUris: string[]): Promise<any> {
    try {
      // Import image analysis service
      const { imageAnalysisService } = await import('./imageAnalysisService');

      // Analyze all images
      const portfolioData = await imageAnalysisService.analyzePortfolioCollection(imageUris);

      // Generate summary
      const summary = await imageAnalysisService.generatePortfolioSummary(portfolioData);

      return summary;

    } catch (error) {
      console.error('Error generating portfolio summary:', error);
      throw error;
    }
  }

  /**
   * Get AI conversation insights
   */
  async getConversationInsights(sessionId: string): Promise<{
    completionRate: number;
    extractedSkills: string[];
    suggestedServices: string[];
    marketInsights: any;
    nextSteps: string[];
  }> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) throw new Error('Session not found');

      const extractedData = session.extractedData || {};

      return {
        completionRate: this.calculateCompletionRate(session),
        extractedSkills: extractedData.skills || [],
        suggestedServices: extractedData.suggestedServices || [],
        marketInsights: extractedData.marketInsights || {},
        nextSteps: this.generateNextSteps(session),
      };

    } catch (error) {
      console.error('Error getting conversation insights:', error);
      throw error;
    }
  }

  /**
   * Calculate completion rate
   */
  private calculateCompletionRate(session: AISession): number {
    const totalSteps = 6; // personal_info, skills, portfolio, services, pricing, review
    const completedSteps = Object.keys(session.extractedData || {}).length;
    return Math.min(1, completedSteps / totalSteps);
  }

  /**
   * Generate next steps
   */
  private generateNextSteps(session: AISession): string[] {
    const extractedData = session.extractedData || {};
    const steps: string[] = [];

    if (!extractedData.personalInfo) {
      steps.push('إكمال المعلومات الشخصية');
    }
    if (!extractedData.skills) {
      steps.push('تحديد المهارات والخبرات');
    }
    if (!extractedData.portfolio) {
      steps.push('رفع أعمال المحفظة');
    }
    if (!extractedData.services) {
      steps.push('تحديد الخدمات المقدمة');
    }
    if (!extractedData.pricing) {
      steps.push('تحديد الأسعار');
    }

    if (steps.length === 0) {
      steps.push('مراجعة الملف الشخصي');
      steps.push('نشر أول خدمة');
    }

    return steps;
  }
}

// Export enhanced service instance
export const enhancedAIChatService = new EnhancedAIChatService();

// Export original service for backward compatibility
export const mobileAIService = new MobileAIService();

-- Row Level Security Policies for Freela Syria
-- These policies ensure users can only access their authorized data

-- Users table policies
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Public user registration" ON users
    FOR INSERT WITH CHECK (true);

-- Expert Profiles policies
CREATE POLICY "Experts can view own profile" ON expert_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Experts can update own profile" ON expert_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Experts can create own profile" ON expert_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Public can view active expert profiles" ON expert_profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = expert_profiles.user_id 
            AND users.status = 'ACTIVE'
        )
    );

-- Client Profiles policies
CREATE POLICY "Clients can view own profile" ON client_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Clients can update own profile" ON client_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Clients can create own profile" ON client_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Service Categories policies (public read access)
CREATE POLICY "Anyone can view service categories" ON service_categories
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage service categories" ON service_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'ADMIN'
        )
    );

-- Services policies
CREATE POLICY "Experts can manage own services" ON services
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM expert_profiles 
            WHERE expert_profiles.id = services.expert_id 
            AND expert_profiles.user_id = auth.uid()
        )
    );

CREATE POLICY "Public can view active services" ON services
    FOR SELECT USING (status = 'ACTIVE');

-- AI Conversation Sessions policies
CREATE POLICY "Users can view own AI sessions" ON ai_conversation_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own AI sessions" ON ai_conversation_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can create own AI sessions" ON ai_conversation_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- AI Conversation Messages policies
CREATE POLICY "Users can view own AI messages" ON ai_conversation_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM ai_conversation_sessions 
            WHERE ai_conversation_sessions.id = ai_conversation_messages.session_id 
            AND ai_conversation_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create AI messages in own sessions" ON ai_conversation_messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM ai_conversation_sessions 
            WHERE ai_conversation_sessions.id = ai_conversation_messages.session_id 
            AND ai_conversation_sessions.user_id = auth.uid()
        )
    );

-- AI Extracted Data policies
CREATE POLICY "Users can view own extracted data" ON ai_extracted_data
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM ai_conversation_sessions 
            WHERE ai_conversation_sessions.id = ai_extracted_data.session_id 
            AND ai_conversation_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create extracted data in own sessions" ON ai_extracted_data
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM ai_conversation_sessions 
            WHERE ai_conversation_sessions.id = ai_extracted_data.session_id 
            AND ai_conversation_sessions.user_id = auth.uid()
        )
    );

-- AI Recommendations policies
CREATE POLICY "Users can view own recommendations" ON ai_recommendations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own recommendations" ON ai_recommendations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can create recommendations for users" ON ai_recommendations
    FOR INSERT WITH CHECK (true); -- Allow system to create recommendations

-- AI Market Intelligence policies (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view market intelligence" ON ai_market_intelligence
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admins can manage market intelligence" ON ai_market_intelligence
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'ADMIN'
        )
    );

-- Create functions for real-time subscriptions
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Create appropriate profile based on user role
    IF NEW.role = 'EXPERT' THEN
        INSERT INTO expert_profiles (user_id, title, description)
        VALUES (
            NEW.id, 
            '{"ar": "", "en": ""}'::jsonb,
            '{"ar": "", "en": ""}'::jsonb
        );
    ELSIF NEW.role = 'CLIENT' THEN
        INSERT INTO client_profiles (user_id)
        VALUES (NEW.id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER handle_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_expert_profiles_updated_at
    BEFORE UPDATE ON expert_profiles
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_client_profiles_updated_at
    BEFORE UPDATE ON client_profiles
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_services_updated_at
    BEFORE UPDATE ON services
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_ai_sessions_updated_at
    BEFORE UPDATE ON ai_conversation_sessions
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_ai_messages_updated_at
    BEFORE UPDATE ON ai_conversation_messages
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_ai_extracted_data_updated_at
    BEFORE UPDATE ON ai_extracted_data
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_ai_recommendations_updated_at
    BEFORE UPDATE ON ai_recommendations
    FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- Function to handle AI session activity updates
CREATE OR REPLACE FUNCTION update_session_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Update last_active_at when new message is added
    UPDATE ai_conversation_sessions 
    SET last_active_at = NOW()
    WHERE id = NEW.session_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for session activity tracking
CREATE TRIGGER on_ai_message_created
    AFTER INSERT ON ai_conversation_messages
    FOR EACH ROW EXECUTE FUNCTION update_session_activity();

-- Create publication for real-time subscriptions
CREATE PUBLICATION supabase_realtime FOR ALL TABLES;

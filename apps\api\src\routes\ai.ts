/**
 * AI Onboarding Routes
 * API endpoints for AI-powered onboarding system
 */

import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { authenticate } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { aiConversationService } from '../services/aiConversation';
import { openRouterService } from '../services/openrouter';
import { enhancedAIService } from '../services/ai/enhancedAIService';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';
import { asyncHandler } from '../utils/asyncHandler';

const router = Router();

/**
 * @swagger
 * /api/ai/conversation/start:
 *   post:
 *     summary: Start new AI conversation session
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userRole
 *               - language
 *             properties:
 *               userRole:
 *                 type: string
 *                 enum: [CLIENT, EXPERT]
 *               language:
 *                 type: string
 *                 enum: [ar, en]
 *               sessionType:
 *                 type: string
 *                 enum: [onboarding, profile_optimization, service_creation]
 *                 default: onboarding
 *     responses:
 *       201:
 *         description: Conversation session started successfully
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Authentication required
 */
router.post(
  '/conversation/start',
  authenticate,
  [
    body('userRole')
      .isIn(['CLIENT', 'EXPERT'])
      .withMessage('User role must be CLIENT or EXPERT'),
    body('language')
      .isIn(['ar', 'en'])
      .withMessage('Language must be ar or en'),
    body('sessionType')
      .optional()
      .isIn(['onboarding', 'profile_optimization', 'service_creation'])
      .withMessage('Invalid session type'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { userRole, language, sessionType = 'onboarding' } = req.body;
    const userId = req.user!.id;

    const session = await aiConversationService.startConversation({
      userId,
      userRole,
      language,
      sessionType,
    });

    logger.info('AI conversation started via API', {
      userId,
      sessionId: session.id,
      userRole,
      language,
      sessionType,
    });

    res.status(201).json({
      success: true,
      message: 'Conversation session started successfully',
      data: {
        sessionId: session.id,
        currentStep: session.currentStep,
        messages: session.messages,
        extractedData: session.extractedData,
        status: session.status,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/conversation/{sessionId}/message:
 *   post:
 *     summary: Send message to AI conversation
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *             properties:
 *               message:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 2000
 *     responses:
 *       200:
 *         description: Message processed successfully
 *       400:
 *         description: Invalid message or session
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Session not found
 */
router.post(
  '/conversation/:sessionId/message',
  authenticate,
  [
    param('sessionId')
      .isString()
      .notEmpty()
      .withMessage('Session ID is required'),
    body('message')
      .isString()
      .trim()
      .isLength({ min: 1, max: 2000 })
      .withMessage('Message must be between 1 and 2000 characters'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { message } = req.body;
    const userId = req.user!.id;

    // Verify session ownership
    const session = aiConversationService.getSession(sessionId);
    if (!session) {
      throw createError.notFound('Conversation session not found');
    }

    if (session.userId !== userId) {
      throw createError.forbidden('Access denied to this conversation session');
    }

    const result = await aiConversationService.processMessage(sessionId, message);

    logger.info('AI message processed via API', {
      userId,
      sessionId,
      messageLength: message.length,
      currentStep: result.session.currentStep,
    });

    res.json({
      success: true,
      message: 'Message processed successfully',
      data: {
        sessionId,
        userMessage: {
          content: message,
          timestamp: new Date(),
        },
        aiResponse: {
          content: result.aiResponse.content,
          timestamp: result.aiResponse.timestamp,
        },
        currentStep: result.session.currentStep,
        extractedData: result.session.extractedData,
        isCompleted: result.session.status === 'completed',
        recommendations: result.session.recommendations,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/conversation/{sessionId}:
 *   get:
 *     summary: Get conversation session details
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Session details retrieved successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Session not found
 */
router.get(
  '/conversation/:sessionId',
  authenticate,
  [
    param('sessionId')
      .isString()
      .notEmpty()
      .withMessage('Session ID is required'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const userId = req.user!.id;

    const session = aiConversationService.getSession(sessionId);
    if (!session) {
      throw createError.notFound('Conversation session not found');
    }

    if (session.userId !== userId) {
      throw createError.forbidden('Access denied to this conversation session');
    }

    res.json({
      success: true,
      message: 'Session details retrieved successfully',
      data: {
        sessionId: session.id,
        sessionType: session.sessionType,
        userRole: session.userRole,
        language: session.language,
        currentStep: session.currentStep,
        status: session.status,
        messages: session.messages,
        extractedData: session.extractedData,
        recommendations: session.recommendations,
        createdAt: session.createdAt,
        lastActiveAt: session.lastActiveAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/conversations:
 *   get:
 *     summary: Get user's conversation sessions
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, completed, abandoned, paused]
 *       - in: query
 *         name: sessionType
 *         schema:
 *           type: string
 *           enum: [onboarding, profile_optimization, service_creation]
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *     responses:
 *       200:
 *         description: User sessions retrieved successfully
 *       401:
 *         description: Authentication required
 */
router.get(
  '/conversations',
  authenticate,
  [
    query('status')
      .optional()
      .isIn(['active', 'completed', 'abandoned', 'paused'])
      .withMessage('Invalid status filter'),
    query('sessionType')
      .optional()
      .isIn(['onboarding', 'profile_optimization', 'service_creation'])
      .withMessage('Invalid session type filter'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { status, sessionType, limit = 20 } = req.query;

    let sessions = aiConversationService.getUserSessions(userId);

    // Apply filters
    if (status) {
      sessions = sessions.filter(session => session.status === status);
    }

    if (sessionType) {
      sessions = sessions.filter(session => session.sessionType === sessionType);
    }

    // Apply limit
    sessions = sessions.slice(0, Number(limit));

    res.json({
      success: true,
      message: 'User sessions retrieved successfully',
      data: {
        sessions: sessions.map(session => ({
          sessionId: session.id,
          sessionType: session.sessionType,
          userRole: session.userRole,
          language: session.language,
          currentStep: session.currentStep,
          status: session.status,
          messageCount: session.messages.length,
          createdAt: session.createdAt,
          lastActiveAt: session.lastActiveAt,
        })),
        total: sessions.length,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/test-connection:
 *   get:
 *     summary: Test OpenRouter API connection
 *     tags: [AI Onboarding]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Connection test successful
 *       500:
 *         description: Connection test failed
 */
router.get(
  '/test-connection',
  authenticate,
  asyncHandler(async (req, res) => {
    const isConnected = await openRouterService.testConnection();
    const availableModels = await openRouterService.getAvailableModels();

    res.json({
      success: true,
      message: 'OpenRouter API connection test completed',
      data: {
        connected: isConnected,
        availableModels: availableModels.length,
        models: availableModels.slice(0, 10), // Return first 10 models
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/enhanced/conversation/start:
 *   post:
 *     summary: Start enhanced AI conversation with advanced features
 *     tags: [Enhanced AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userRole
 *               - sessionType
 *             properties:
 *               userRole:
 *                 type: string
 *                 enum: [CLIENT, EXPERT]
 *               sessionType:
 *                 type: string
 *                 enum: [onboarding, profile_optimization, service_creation]
 *     responses:
 *       201:
 *         description: Enhanced conversation session started successfully
 */
router.post(
  '/enhanced/conversation/start',
  authenticate,
  [
    body('userRole')
      .isIn(['CLIENT', 'EXPERT'])
      .withMessage('User role must be CLIENT or EXPERT'),
    body('sessionType')
      .isIn(['onboarding', 'profile_optimization', 'service_creation'])
      .withMessage('Invalid session type'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { userRole, sessionType } = req.body;
    const userId = req.user!.id;

    const result = await enhancedAIService.startEnhancedConversation(
      userId,
      sessionType,
      userRole
    );

    logger.info('Enhanced AI conversation started', {
      userId,
      sessionId: result.sessionId,
      userRole,
      sessionType,
    });

    res.status(201).json({
      success: true,
      message: 'Enhanced conversation session started successfully',
      data: {
        sessionId: result.sessionId,
        welcomeMessage: result.welcomeMessage,
        currentStep: result.currentStep,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/enhanced/conversation/{sessionId}/message:
 *   post:
 *     summary: Send message to enhanced AI conversation with confidence scoring
 *     tags: [Enhanced AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *               - currentStep
 *             properties:
 *               message:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 2000
 *               currentStep:
 *                 type: string
 *     responses:
 *       200:
 *         description: Message processed with AI analysis
 */
router.post(
  '/enhanced/conversation/:sessionId/message',
  authenticate,
  [
    param('sessionId')
      .isString()
      .notEmpty()
      .withMessage('Session ID is required'),
    body('message')
      .isString()
      .trim()
      .isLength({ min: 1, max: 2000 })
      .withMessage('Message must be between 1 and 2000 characters'),
    body('currentStep')
      .isString()
      .notEmpty()
      .withMessage('Current step is required'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { message, currentStep } = req.body;
    const userId = req.user!.id;

    const result = await enhancedAIService.processUserMessage(
      sessionId,
      message,
      currentStep
    );

    logger.info('Enhanced AI message processed', {
      userId,
      sessionId,
      currentStep,
      confidence: result.confidence,
      nextStep: result.nextStep,
    });

    res.json({
      success: true,
      message: 'Message processed with AI analysis',
      data: {
        aiResponse: result.aiResponse,
        nextStep: result.nextStep,
        extractedData: result.extractedData,
        confidence: result.confidence,
        recommendations: result.recommendations,
      },
    });
  })
);

/**
 * @swagger
 * /api/ai/enhanced/profile/generate/{sessionId}:
 *   post:
 *     summary: Generate expert profile from AI conversation data
 *     tags: [Enhanced AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Expert profile generated successfully
 */
router.post(
  '/enhanced/profile/generate/:sessionId',
  authenticate,
  [
    param('sessionId')
      .isString()
      .notEmpty()
      .withMessage('Session ID is required'),
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const userId = req.user!.id;

    const generatedProfile = await enhancedAIService.generateExpertProfile(sessionId);

    logger.info('Expert profile generated from AI data', {
      userId,
      sessionId,
      profileGenerated: true,
    });

    res.json({
      success: true,
      message: 'Expert profile generated successfully',
      data: {
        profile: generatedProfile,
        sessionId,
      },
    });
  })
);

export default router;

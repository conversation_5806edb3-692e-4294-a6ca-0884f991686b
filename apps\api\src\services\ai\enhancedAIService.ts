// Enhanced AI Service with Supabase Integration
// Phase 2: Advanced AI features with confidence scoring and market intelligence

import { supabase, supabaseAdmin, SupabaseAIChat } from '@freela/database/src/supabase';
import { OpenRouterService } from './openRouterService';

interface SkillExtractionResult {
  skills: Array<{
    name: string;
    category: string;
    confidence: number;
    evidence: string[];
  }>;
  experience: {
    years: number;
    confidence: number;
    projects: string[];
  };
  pricing: {
    suggested_rate: number;
    confidence: number;
    market_analysis: MarketData;
  };
}

interface MarketData {
  category: string;
  average_rate: number;
  demand_level: 'low' | 'medium' | 'high';
  competition_level: number;
  trending_skills: string[];
}

interface ConfidenceMetrics {
  overall_confidence: number;
  data_completeness: number;
  extraction_accuracy: number;
  market_alignment: number;
  validation_required: boolean;
  improvement_suggestions: string[];
}

export class EnhancedAIService {
  private openRouter: OpenRouterService;

  constructor() {
    this.openRouter = new OpenRouterService();
  }

  /**
   * Start an enhanced AI conversation session with advanced features
   */
  async startEnhancedConversation(userId: string, sessionType: string, userRole: 'CLIENT' | 'EXPERT') {
    try {
      // Create session in Supabase
      const { data: session, error } = await supabaseAdmin
        .from('ai_conversation_sessions')
        .insert({
          user_id: userId,
          session_type: sessionType,
          user_role: userRole,
          current_step: 'welcome',
          status: 'active',
          ai_model: 'openai/gpt-4-turbo-preview',
          temperature: 0.7,
          max_tokens: 1500,
          total_steps: userRole === 'EXPERT' ? 8 : 6
        })
        .select()
        .single();

      if (error) throw error;

      // Initialize AI chat helper
      const aiChat = new SupabaseAIChat(session.id);

      // Send welcome message based on user role
      const welcomeMessage = await this.generateWelcomeMessage(userRole, session.id);
      await aiChat.sendMessage(welcomeMessage.content, 'assistant', 'welcome');

      return {
        sessionId: session.id,
        welcomeMessage: welcomeMessage.content,
        currentStep: 'welcome',
        aiChat
      };
    } catch (error) {
      console.error('Error starting enhanced AI conversation:', error);
      throw error;
    }
  }

  /**
   * Process user message with advanced NLP and confidence scoring
   */
  async processUserMessage(sessionId: string, userMessage: string, currentStep: string) {
    try {
      const aiChat = new SupabaseAIChat(sessionId);

      // Save user message
      await aiChat.sendMessage(userMessage, 'user', 'text');

      // Get session context
      const { data: session } = await supabaseAdmin
        .from('ai_conversation_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (!session) throw new Error('Session not found');

      // Extract data with confidence scoring
      const extractionResult = await this.extractDataWithConfidence(
        userMessage,
        currentStep,
        session.user_role,
        session.extracted_data
      );

      // Update session with extracted data
      const updatedExtractedData = {
        ...session.extracted_data,
        [currentStep]: extractionResult
      };

      // Generate AI response with market intelligence
      const aiResponse = await this.generateIntelligentResponse(
        userMessage,
        currentStep,
        session.user_role,
        extractionResult,
        updatedExtractedData
      );

      // Save AI response
      await aiChat.sendMessage(aiResponse.content, 'assistant', 'response');

      // Update session
      await aiChat.updateSession({
        current_step: aiResponse.nextStep,
        extracted_data: updatedExtractedData,
        completion_rate: this.calculateCompletionRate(aiResponse.nextStep, session.total_steps)
      });

      // Generate recommendations if applicable
      if (extractionResult.confidence < 0.7) {
        await this.generateImprovementRecommendations(sessionId, session.user_id, extractionResult);
      }

      return {
        aiResponse: aiResponse.content,
        nextStep: aiResponse.nextStep,
        extractedData: extractionResult,
        confidence: extractionResult.confidence,
        recommendations: aiResponse.recommendations
      };
    } catch (error) {
      console.error('Error processing user message:', error);
      throw error;
    }
  }

  /**
   * Extract data with advanced NLP and confidence scoring
   */
  private async extractDataWithConfidence(
    userMessage: string,
    currentStep: string,
    userRole: 'CLIENT' | 'EXPERT',
    existingData: any
  ): Promise<any> {
    const extractionPrompt = this.buildExtractionPrompt(userMessage, currentStep, userRole, existingData);

    const response = await this.openRouter.generateResponse(extractionPrompt, {
      model: 'openai/gpt-4-turbo-preview',
      temperature: 0.3,
      max_tokens: 1000
    });

    try {
      const extractedData = JSON.parse(response);
      
      // Calculate confidence score
      const confidence = this.calculateConfidenceScore(extractedData, userMessage);
      
      return {
        ...extractedData,
        confidence,
        extraction_timestamp: new Date().toISOString(),
        original_message: userMessage
      };
    } catch (error) {
      console.error('Error parsing extracted data:', error);
      return {
        raw_text: userMessage,
        confidence: 0.3,
        error: 'Failed to extract structured data'
      };
    }
  }

  /**
   * Generate intelligent response with market intelligence
   */
  private async generateIntelligentResponse(
    userMessage: string,
    currentStep: string,
    userRole: 'CLIENT' | 'EXPERT',
    extractionResult: any,
    allExtractedData: any
  ) {
    // Get market intelligence for context
    const marketData = await this.getMarketIntelligence(extractionResult, userRole);

    const responsePrompt = this.buildResponsePrompt(
      userMessage,
      currentStep,
      userRole,
      extractionResult,
      allExtractedData,
      marketData
    );

    const response = await this.openRouter.generateResponse(responsePrompt, {
      model: 'openai/gpt-4-turbo-preview',
      temperature: 0.7,
      max_tokens: 1200
    });

    try {
      const parsedResponse = JSON.parse(response);
      return {
        content: parsedResponse.message,
        nextStep: parsedResponse.next_step,
        recommendations: parsedResponse.recommendations || []
      };
    } catch (error) {
      return {
        content: response,
        nextStep: this.getNextStep(currentStep, userRole),
        recommendations: []
      };
    }
  }

  /**
   * Get market intelligence for pricing and demand analysis
   */
  private async getMarketIntelligence(extractionResult: any, userRole: 'CLIENT' | 'EXPERT'): Promise<MarketData | null> {
    if (userRole !== 'EXPERT' || !extractionResult.skills) return null;

    try {
      const { data: marketData } = await supabaseAdmin
        .from('ai_market_intelligence')
        .select('*')
        .eq('category', extractionResult.category || 'general')
        .order('last_updated', { ascending: false })
        .limit(1)
        .single();

      return marketData || null;
    } catch (error) {
      console.error('Error fetching market intelligence:', error);
      return null;
    }
  }

  /**
   * Calculate confidence score based on extraction quality
   */
  private calculateConfidenceScore(extractedData: any, originalMessage: string): number {
    let confidence = 0.5; // Base confidence

    // Check data completeness
    const requiredFields = ['skills', 'experience', 'category'];
    const completedFields = requiredFields.filter(field => extractedData[field]);
    confidence += (completedFields.length / requiredFields.length) * 0.3;

    // Check message length and detail
    if (originalMessage.length > 50) confidence += 0.1;
    if (originalMessage.length > 100) confidence += 0.1;

    // Check for specific keywords
    const skillKeywords = ['experience', 'years', 'project', 'client', 'work'];
    const foundKeywords = skillKeywords.filter(keyword => 
      originalMessage.toLowerCase().includes(keyword)
    );
    confidence += (foundKeywords.length / skillKeywords.length) * 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Generate improvement recommendations for low confidence extractions
   */
  private async generateImprovementRecommendations(sessionId: string, userId: string, extractionResult: any) {
    const recommendations = [];

    if (extractionResult.confidence < 0.5) {
      recommendations.push({
        type: 'data_quality',
        priority: 'high',
        title: 'تحسين جودة البيانات',
        description: 'يرجى تقديم المزيد من التفاصيل حول خبرتك ومهاراتك',
        action_required: {
          type: 'provide_more_details',
          fields: ['experience_details', 'project_examples', 'skill_specifics']
        }
      });
    }

    if (!extractionResult.skills || extractionResult.skills.length === 0) {
      recommendations.push({
        type: 'skill_identification',
        priority: 'high',
        title: 'تحديد المهارات',
        description: 'لم نتمكن من تحديد مهاراتك بوضوح. يرجى ذكر مهاراتك الأساسية',
        action_required: {
          type: 'list_skills',
          examples: ['تصميم جرافيكي', 'برمجة', 'كتابة محتوى', 'ترجمة']
        }
      });
    }

    // Save recommendations to database
    for (const rec of recommendations) {
      await supabaseAdmin
        .from('ai_recommendations')
        .insert({
          session_id: sessionId,
          user_id: userId,
          type: rec.type,
          category: 'onboarding_improvement',
          priority: rec.priority,
          title: rec.title,
          description: rec.description,
          recommendation_data: rec,
          confidence_score: 0.9,
          status: 'pending'
        });
    }
  }

  /**
   * Auto-generate expert profile from AI conversation data
   */
  async generateExpertProfile(sessionId: string): Promise<any> {
    try {
      // Get session data
      const { data: session } = await supabaseAdmin
        .from('ai_conversation_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (!session || session.user_role !== 'EXPERT') {
        throw new Error('Invalid session for expert profile generation');
      }

      // Generate profile using AI
      const profilePrompt = this.buildProfileGenerationPrompt(session.extracted_data);
      
      const response = await this.openRouter.generateResponse(profilePrompt, {
        model: 'openai/gpt-4-turbo-preview',
        temperature: 0.6,
        max_tokens: 1500
      });

      const generatedProfile = JSON.parse(response);

      // Update session with generated profile
      await supabaseAdmin
        .from('ai_conversation_sessions')
        .update({
          profile_data: generatedProfile,
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      return generatedProfile;
    } catch (error) {
      console.error('Error generating expert profile:', error);
      throw error;
    }
  }

  /**
   * Build extraction prompt for NLP processing
   */
  private buildExtractionPrompt(userMessage: string, currentStep: string, userRole: string, existingData: any): string {
    return `
أنت خبير في استخراج البيانات من المحادثات العربية. استخرج المعلومات التالية من رسالة المستخدم:

رسالة المستخدم: "${userMessage}"
الخطوة الحالية: ${currentStep}
نوع المستخدم: ${userRole}
البيانات الموجودة: ${JSON.stringify(existingData)}

استخرج البيانات في تنسيق JSON مع الحقول التالية:
{
  "skills": ["مهارة 1", "مهارة 2"],
  "experience": {
    "years": عدد_السنوات,
    "level": "مبتدئ|متوسط|خبير",
    "projects": ["مشروع 1", "مشروع 2"]
  },
  "category": "فئة_الخدمة",
  "pricing": {
    "hourly_rate": السعر_بالساعة,
    "currency": "USD|SYP"
  },
  "availability": "متاح|مشغول|جزئي",
  "confidence_indicators": ["مؤشر 1", "مؤشر 2"]
}

تأكد من استخراج البيانات بدقة وتقديم مؤشرات الثقة.
`;
  }

  /**
   * Build response generation prompt
   */
  private buildResponsePrompt(
    userMessage: string,
    currentStep: string,
    userRole: string,
    extractionResult: any,
    allData: any,
    marketData: any
  ): string {
    return `
أنت مساعد ذكي متخصص في إعداد الملفات الشخصية على منصة فريلا سوريا.

رسالة المستخدم: "${userMessage}"
الخطوة الحالية: ${currentStep}
نوع المستخدم: ${userRole}
البيانات المستخرجة: ${JSON.stringify(extractionResult)}
جميع البيانات: ${JSON.stringify(allData)}
بيانات السوق: ${JSON.stringify(marketData)}

اكتب ردًا ذكيًا يتضمن:
1. تأكيد فهم المعلومات المقدمة
2. اقتراحات للتحسين بناءً على بيانات السوق
3. السؤال التالي المناسب
4. توصيات شخصية

تنسيق الرد:
{
  "message": "الرد باللغة العربية",
  "next_step": "اسم_الخطوة_التالية",
  "recommendations": [
    {
      "type": "نوع_التوصية",
      "content": "محتوى_التوصية",
      "priority": "عالي|متوسط|منخفض"
    }
  ]
}
`;
  }

  /**
   * Build profile generation prompt
   */
  private buildProfileGenerationPrompt(extractedData: any): string {
    return `
بناءً على البيانات المستخرجة من المحادثة، اكتب ملفًا شخصيًا احترافيًا للخبير:

البيانات المستخرجة: ${JSON.stringify(extractedData)}

اكتب الملف الشخصي في تنسيق JSON:
{
  "title": {
    "ar": "العنوان باللغة العربية",
    "en": "Title in English"
  },
  "description": {
    "ar": "الوصف باللغة العربية (200-300 كلمة)",
    "en": "Description in English (200-300 words)"
  },
  "skills": ["مهارة 1", "مهارة 2", "مهارة 3"],
  "experience_level": "مبتدئ|متوسط|خبير",
  "hourly_rate": السعر_المقترح,
  "services": [
    {
      "title": {"ar": "عنوان الخدمة", "en": "Service Title"},
      "description": {"ar": "وصف الخدمة", "en": "Service Description"},
      "pricing": {"basic": 50, "standard": 100, "premium": 200},
      "delivery_time": عدد_الأيام
    }
  ]
}

تأكد من أن الملف الشخصي احترافي وجذاب ومناسب للسوق السوري.
`;
  }

  /**
   * Calculate completion rate based on current step
   */
  private calculateCompletionRate(currentStep: string, totalSteps: number): number {
    const stepOrder = ['welcome', 'skills', 'experience', 'portfolio', 'pricing', 'services', 'review', 'complete'];
    const currentIndex = stepOrder.indexOf(currentStep);
    return currentIndex >= 0 ? (currentIndex / totalSteps) : 0;
  }

  /**
   * Get next step in the conversation flow
   */
  private getNextStep(currentStep: string, userRole: 'CLIENT' | 'EXPERT'): string {
    const expertFlow = ['welcome', 'skills', 'experience', 'portfolio', 'pricing', 'services', 'review', 'complete'];
    const clientFlow = ['welcome', 'needs', 'budget', 'timeline', 'preferences', 'complete'];
    
    const flow = userRole === 'EXPERT' ? expertFlow : clientFlow;
    const currentIndex = flow.indexOf(currentStep);
    
    return currentIndex >= 0 && currentIndex < flow.length - 1 
      ? flow[currentIndex + 1] 
      : 'complete';
  }

  /**
   * Generate welcome message based on user role
   */
  private async generateWelcomeMessage(userRole: 'CLIENT' | 'EXPERT', sessionId: string) {
    const welcomePrompts = {
      EXPERT: `مرحباً بك في فريلا سوريا! أنا مساعدك الذكي وسأساعدك في إنشاء ملف شخصي احترافي يجذب العملاء. 

سأقوم بطرح بعض الأسئلة لفهم خبراتك ومهاراتك، ثم سأساعدك في:
✅ إنشاء ملف شخصي مميز
✅ تحديد أسعار تنافسية
✅ كتابة وصف خدماتك
✅ تحسين فرص الحصول على مشاريع

لنبدأ! ما هي الخدمة الأساسية التي تريد تقديمها على المنصة؟`,

      CLIENT: `أهلاً وسهلاً بك في فريلا سوريا! أنا مساعدك الذكي وسأساعدك في العثور على أفضل الخبراء لمشروعك.

سأقوم بفهم احتياجاتك ثم أساعدك في:
✅ تحديد نوع الخدمة المطلوبة
✅ وضع ميزانية مناسبة
✅ اختيار الخبير المناسب
✅ إدارة مشروعك بنجاح

ما هو المشروع الذي تريد تنفيذه؟`
    };

    return {
      content: welcomePrompts[userRole],
      step: 'welcome'
    };
  }
}

export const enhancedAIService = new EnhancedAIService();

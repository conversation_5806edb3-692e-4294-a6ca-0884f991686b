{"name": "@freela/mobile", "version": "1.0.0", "description": "Freela Syria Mobile App - React Native", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "dev": "react-native start", "web": "node web-server.js", "web:dev": "node web-server.js --dev", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "react-native clean", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace FreelaApp.xcworkspace -scheme FreelaApp -configuration Release archive", "bundle:android": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle", "bundle:ios": "react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle"}, "dependencies": {"@freela/i18n": "file:../../packages/i18n", "@freela/types": "file:../../packages/types", "@freela/utils": "file:../../packages/utils", "@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/netinfo": "^11.2.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "date-fns": "^2.30.0", "i18next": "^23.7.6", "react": "18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "react-native": "^0.72.17", "react-native-device-info": "^10.11.0", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "^2.14.0", "react-native-image-picker": "^7.0.3", "react-native-keychain": "^8.1.3", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.0.4", "react-native-modal": "^13.0.1", "react-native-paper": "^5.11.3", "react-native-permissions": "^4.0.4", "react-native-reanimated": "^3.6.0", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.27.0", "react-native-svg": "^14.0.0", "react-native-toast-message": "^2.1.7", "react-native-vector-icons": "^10.0.2", "socket.io-client": "^4.7.4", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.23.5", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/preset-env": "^7.23.5", "@babel/runtime": "^7.23.5", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@types/react": "^18.2.38", "@types/react-native": "^0.72.7", "@types/react-test-renderer": "^18.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "babel-jest": "^29.7.0", "babel-plugin-module-resolver": "^5.0.0", "cors": "^2.8.5", "eslint": "^8.54.0", "eslint-config-prettier": "^10.1.5", "express": "^4.21.2", "jest": "^29.7.0", "metro-react-native-babel-preset": "0.76.8", "prettier": "^3.1.0", "react-dom": "^18.2.0", "react-native-svg-transformer": "^1.5.1", "react-native-web": "^0.19.9", "react-test-renderer": "18.2.0", "typescript": "^5.3.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "engines": {"node": ">=18.0.0"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}}
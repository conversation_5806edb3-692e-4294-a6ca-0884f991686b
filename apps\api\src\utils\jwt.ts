/**
 * JWT Utility for Freela Syria API
 * Provides comprehensive JWT token management with blacklisting support
 */

import jwt, { SignOptions } from 'jsonwebtoken';
import { createClient } from 'redis';
import { AppError, createError } from './errors';

// JWT Payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  role: 'CLIENT' | 'EXPERT' | 'ADMIN';
  sessionId?: string;
  iat?: number;
  exp?: number;
}

// JWT Options interface
export interface JWTOptions {
  expiresIn?: string | number;
  audience?: string;
  issuer?: string;
}

// Token pair interface
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  refreshExpiresIn: number;
}

// JWT Configuration from environment
const JWT_CONFIG = {
  ACCESS_SECRET: process.env.JWT_ACCESS_SECRET || 'freela-syria-access-secret-2024',
  REFRESH_SECRET: process.env.JWT_REFRESH_SECRET || 'freela-syria-refresh-secret-2024',
  ACCESS_EXPIRES_IN: process.env.JWT_ACCESS_EXPIRES_IN || '15m',
  REFRESH_EXPIRES_IN: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  ISSUER: process.env.JWT_ISSUER || 'freela-syria-api',
  AUDIENCE: process.env.JWT_AUDIENCE || 'freela-syria-app',
};

// Redis client for token blacklisting (optional)
let redisClient: ReturnType<typeof createClient> | null = null;

// Initialize Redis client if available
const initializeRedis = async () => {
  try {
    if (process.env.REDIS_URL) {
      redisClient = createClient({
        url: process.env.REDIS_URL,
      });
      await redisClient.connect();
      console.log('Redis connected for JWT blacklisting');
    }
  } catch (error) {
    console.warn('Redis not available for JWT blacklisting:', (error as Error).message);
    redisClient = null;
  }
};

// Initialize Redis on module load
initializeRedis();

/**
 * JWT Utilities Object
 */
export const jwtUtils = {
  /**
   * Generate access token
   */
  generateAccessToken: (payload: Omit<JWTPayload, 'iat' | 'exp'>): string => {
    try {
      const options: SignOptions = {
        expiresIn: JWT_CONFIG.ACCESS_EXPIRES_IN,
        issuer: JWT_CONFIG.ISSUER,
        audience: JWT_CONFIG.AUDIENCE,
      };
      return jwt.sign(payload, JWT_CONFIG.ACCESS_SECRET, options);
    } catch (error) {
      throw createError.internalError(
        { operation: 'generateAccessToken', error: (error as Error).message },
        'فشل في إنشاء رمز الوصول'
      );
    }
  },

  /**
   * Generate refresh token
   */
  generateRefreshToken: (payload: Omit<JWTPayload, 'iat' | 'exp'>): string => {
    try {
      const options: SignOptions = {
        expiresIn: JWT_CONFIG.REFRESH_EXPIRES_IN as string,
        issuer: JWT_CONFIG.ISSUER,
        audience: JWT_CONFIG.AUDIENCE,
      };
      return jwt.sign(payload, JWT_CONFIG.REFRESH_SECRET, options);
    } catch (error) {
      throw createError.internalError(
        { operation: 'generateRefreshToken', error: (error as Error).message },
        'فشل في إنشاء رمز التحديث'
      );
    }
  },

  /**
   * Generate token pair (access + refresh)
   */
  generateTokenPair: (payload: Omit<JWTPayload, 'iat' | 'exp'>): TokenPair => {
    const accessToken = jwtUtils.generateAccessToken(payload);
    const refreshToken = jwtUtils.generateRefreshToken(payload);

    // Calculate expiration times in seconds
    const accessExpiresIn = jwtUtils.getTokenExpirationTime(JWT_CONFIG.ACCESS_EXPIRES_IN);
    const refreshExpiresIn = jwtUtils.getTokenExpirationTime(JWT_CONFIG.REFRESH_EXPIRES_IN);

    return {
      accessToken,
      refreshToken,
      expiresIn: accessExpiresIn,
      refreshExpiresIn: refreshExpiresIn,
    };
  },

  /**
   * Verify access token
   */
  verifyAccessToken: async (token: string): Promise<JWTPayload> => {
    try {
      // Check if token is blacklisted
      if (await jwtUtils.isTokenBlacklisted(token)) {
        throw createError.tokenInvalid('الرمز المميز محظور');
      }

      const decoded = jwt.verify(token, JWT_CONFIG.ACCESS_SECRET, {
        issuer: JWT_CONFIG.ISSUER,
        audience: JWT_CONFIG.AUDIENCE,
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }

      const err = error as any;
      if (err.name === 'TokenExpiredError') {
        throw createError.tokenExpired();
      }

      if (err.name === 'JsonWebTokenError') {
        throw createError.tokenInvalid();
      }

      throw createError.internalError(
        { operation: 'verifyAccessToken', error: (error as Error).message },
        'فشل في التحقق من رمز الوصول'
      );
    }
  },

  /**
   * Verify refresh token
   */
  verifyRefreshToken: async (token: string): Promise<JWTPayload> => {
    try {
      // Check if token is blacklisted
      if (await jwtUtils.isTokenBlacklisted(token)) {
        throw createError.tokenInvalid('رمز التحديث محظور');
      }

      const decoded = jwt.verify(token, JWT_CONFIG.REFRESH_SECRET, {
        issuer: JWT_CONFIG.ISSUER,
        audience: JWT_CONFIG.AUDIENCE,
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }

      const err = error as any;
      if (err.name === 'TokenExpiredError') {
        throw createError.tokenExpired();
      }

      if (err.name === 'JsonWebTokenError') {
        throw createError.tokenInvalid();
      }

      throw createError.internalError(
        { operation: 'verifyRefreshToken', error: (error as Error).message },
        'فشل في التحقق من رمز التحديث'
      );
    }
  },

  /**
   * Refresh access token using refresh token
   */
  refreshAccessToken: async (refreshToken: string): Promise<TokenPair> => {
    const payload = await jwtUtils.verifyRefreshToken(refreshToken);
    
    // Generate new token pair
    const newTokenPair = jwtUtils.generateTokenPair({
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      sessionId: payload.sessionId,
    });

    // Blacklist the old refresh token
    await jwtUtils.blacklistToken(refreshToken);

    return newTokenPair;
  },

  /**
   * Blacklist a token (logout)
   */
  blacklistToken: async (token: string): Promise<void> => {
    if (!redisClient) {
      console.warn('Redis not available for token blacklisting');
      return;
    }

    try {
      // Decode token to get expiration time
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await redisClient.setEx(`blacklist:${token}`, ttl, 'true');
        }
      }
    } catch (error) {
      console.error('Error blacklisting token:', (error as Error).message);
    }
  },

  /**
   * Check if token is blacklisted
   */
  isTokenBlacklisted: async (token: string): Promise<boolean> => {
    if (!redisClient) {
      return false; // If Redis is not available, assume token is not blacklisted
    }

    try {
      const result = await redisClient.get(`blacklist:${token}`);
      return result === 'true';
    } catch (error) {
      console.error('Error checking token blacklist:', (error as Error).message);
      return false; // Fail open - assume token is not blacklisted
    }
  },

  /**
   * Get token expiration time in seconds
   */
  getTokenExpirationTime: (expiresIn: string | number): number => {
    if (typeof expiresIn === 'number') {
      return expiresIn;
    }

    // Parse string format like '15m', '7d', '1h'
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) {
      throw new Error(`Invalid expiresIn format: ${expiresIn}`);
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 60 * 60 * 24;
      default: throw new Error(`Invalid time unit: ${unit}`);
    }
  },

  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader: (authHeader?: string): string | null => {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  },
};

// Export default as jwtUtils for convenience
export default jwtUtils;

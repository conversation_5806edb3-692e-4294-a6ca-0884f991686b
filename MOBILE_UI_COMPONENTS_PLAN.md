# 📱 Mobile UI Components - Comprehensive Implementation Plan
# خطة تطبيق مكونات واجهة المستخدم للهاتف المحمول

## 📋 **OVERVIEW**

**Objective**: Implement missing mobile UI components for Phase 3 AI features  
**Priority**: **HIGH** - Core functionality completion  
**Timeline**: 6 days (Days 6-11 of master plan)  
**Dependencies**: API Infrastructure fixes must be completed first

---

## 🎯 **MISSING COMPONENTS ANALYSIS**

### **Critical Missing Components**
1. **VoiceRecordingButton** - Glass morphism voice input interface
2. **ImageUploadCard** - AI-powered image analysis interface  
3. **EnhancedChatBubble** - Voice/image message support
4. **AIProcessingIndicator** - Loading states for AI operations
5. **AIInsightsPanel** - Display AI analysis results
6. **VoiceWaveform** - Audio visualization during recording
7. **ImageAnalysisOverlay** - Real-time analysis feedback
8. **ConfidenceScoreDisplay** - AI confidence visualization

### **Enhancement Requirements**
- **Design Consistency**: Match landing page glass morphism standards
- **Theme Integration**: Support Gold/Purple dual-theme system
- **Arabic RTL**: Complete right-to-left layout support
- **Typography**: Cairo/Tajawal font integration
- **Animations**: Smooth 60fps interactions
- **Accessibility**: WCAG 2.1 AA compliance

---

## 🛠️ **IMPLEMENTATION PHASES**

### **Phase 1: Voice Recording Interface (Days 6-8)**

#### **1.1 VoiceRecordingButton Component**
**File**: `apps/mobile/src/components/ai/VoiceRecordingButton.tsx`

```typescript
// Component Structure:
interface VoiceRecordingButtonProps {
  onStartRecording: () => void;
  onStopRecording: () => void;
  isRecording: boolean;
  isProcessing: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  theme?: 'gold' | 'purple';
  style?: ViewStyle;
}

// Features:
- Glass morphism circular button design
- Pulsing animation during recording
- Microphone icon with state transitions
- Touch feedback with haptics
- Recording duration display
- Error state handling
```

**Design Specifications**:
- **Size**: 80px diameter (large), 60px (medium), 40px (small)
- **Glass Effect**: `backdrop-blur(25px)`, theme-based background
- **Animation**: Pulse scale 1.0 → 1.1 → 1.0 (1s cycle)
- **Colors**: Gold theme `rgba(255, 215, 0, 0.12)`, Purple theme `rgba(217, 70, 239, 0.12)`
- **Border**: 1px solid with theme-based color
- **Shadow**: Premium shadow with theme tint

#### **1.2 VoiceWaveform Component**
**File**: `apps/mobile/src/components/ai/VoiceWaveform.tsx`

```typescript
// Component Structure:
interface VoiceWaveformProps {
  audioLevels: number[];
  isRecording: boolean;
  duration: number;
  maxDuration: number;
  theme?: 'gold' | 'purple';
}

// Features:
- Real-time audio level visualization
- Animated bars with theme colors
- Recording progress indicator
- Maximum duration warning
- Smooth 60fps animations
```

#### **1.3 VoiceMessageBubble Component**
**File**: `apps/mobile/src/components/ai/VoiceMessageBubble.tsx`

```typescript
// Component Structure:
interface VoiceMessageBubbleProps {
  audioUri: string;
  transcript?: string;
  confidence?: number;
  duration: number;
  isPlaying: boolean;
  onPlay: () => void;
  onPause: () => void;
  isOwn: boolean;
  theme?: 'gold' | 'purple';
}

// Features:
- Glass morphism message bubble
- Play/pause controls with animations
- Transcript display with confidence score
- Waveform visualization
- Arabic RTL text support
```

### **Phase 2: Image Upload Interface (Days 8-10)**

#### **2.1 ImageUploadCard Component**
**File**: `apps/mobile/src/components/ai/ImageUploadCard.tsx`

```typescript
// Component Structure:
interface ImageUploadCardProps {
  onImageSelect: (source: 'camera' | 'gallery') => void;
  onImageUpload: (imageUri: string) => void;
  isAnalyzing: boolean;
  analysisResult?: ImageAnalysisResult;
  error?: string;
  theme?: 'gold' | 'purple';
  style?: ViewStyle;
}

// Features:
- Glass morphism card design
- Camera/gallery selection buttons
- Drag-drop area for images
- Preview with analysis overlay
- Progress indicator during analysis
- Error state with retry option
```

**Design Specifications**:
- **Layout**: Rounded corners (16px), glass background
- **Buttons**: Camera and gallery icons with glass effect
- **Preview**: Image with analysis overlay
- **Progress**: Circular progress with theme colors
- **Error**: Red tint with Arabic error message

#### **2.2 ImageAnalysisOverlay Component**
**File**: `apps/mobile/src/components/ai/ImageAnalysisOverlay.tsx`

```typescript
// Component Structure:
interface ImageAnalysisOverlayProps {
  imageUri: string;
  analysis: ImageAnalysisResult;
  isVisible: boolean;
  onClose: () => void;
  theme?: 'gold' | 'purple';
}

// Features:
- Full-screen overlay with glass background
- Image with analysis annotations
- Skills and quality score display
- Pricing suggestions
- Swipe-to-dismiss gesture
```

#### **2.3 ImageMessageBubble Component**
**File**: `apps/mobile/src/components/ai/ImageMessageBubble.tsx`

```typescript
// Component Structure:
interface ImageMessageBubbleProps {
  imageUri: string;
  caption?: string;
  analysis?: ImageAnalysisResult;
  isOwn: boolean;
  onImagePress: () => void;
  theme?: 'gold' | 'purple';
}

// Features:
- Glass morphism image container
- Caption with Arabic RTL support
- Analysis summary display
- Tap to expand functionality
- Loading state for analysis
```

### **Phase 3: Enhanced Chat Interface (Days 9-11)**

#### **3.1 EnhancedChatInput Component**
**File**: `apps/mobile/src/components/ai/EnhancedChatInput.tsx`

```typescript
// Component Structure:
interface EnhancedChatInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  onVoiceRecord: () => void;
  onImageUpload: () => void;
  isRecording: boolean;
  isProcessing: boolean;
  disabled?: boolean;
  theme?: 'gold' | 'purple';
}

// Features:
- Multi-line text input with glass background
- Voice recording button integration
- Image upload button
- Send button with animation
- Typing indicator
- Character count display
```

#### **3.2 AIProcessingIndicator Component**
**File**: `apps/mobile/src/components/ai/AIProcessingIndicator.tsx`

```typescript
// Component Structure:
interface AIProcessingIndicatorProps {
  type: 'voice' | 'image' | 'text' | 'matching';
  progress?: number;
  message?: string;
  theme?: 'gold' | 'purple';
  size?: 'small' | 'medium' | 'large';
}

// Features:
- Animated loading spinner with theme colors
- Progress bar for long operations
- Status message in Arabic/English
- Different animations per operation type
- Smooth transitions
```

#### **3.3 ConfidenceScoreDisplay Component**
**File**: `apps/mobile/src/components/ai/ConfidenceScoreDisplay.tsx`

```typescript
// Component Structure:
interface ConfidenceScoreDisplayProps {
  score: number; // 0-1
  label?: string;
  showPercentage?: boolean;
  size?: 'small' | 'medium' | 'large';
  theme?: 'gold' | 'purple';
}

// Features:
- Circular progress indicator
- Color-coded confidence levels
- Animated score counting
- Arabic/English labels
- Tooltip with explanation
```

---

## 🎨 **DESIGN SYSTEM INTEGRATION**

### **Glass Morphism Standards**
```typescript
// Base glass effect styles
const glassStyles = {
  backgroundColor: 'rgba(255, 255, 255, 0.08)',
  backdropFilter: 'blur(25px)',
  borderWidth: 1,
  borderColor: 'rgba(255, 255, 255, 0.15)',
  borderRadius: 16,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 8 },
  shadowOpacity: 0.1,
  shadowRadius: 32,
  elevation: 8,
};

// Theme-specific variations
const goldGlass = {
  ...glassStyles,
  backgroundColor: 'rgba(255, 215, 0, 0.12)',
  borderColor: 'rgba(255, 215, 0, 0.25)',
  shadowColor: '#FFD700',
  shadowOpacity: 0.15,
};

const purpleGlass = {
  ...glassStyles,
  backgroundColor: 'rgba(217, 70, 239, 0.12)',
  borderColor: 'rgba(217, 70, 239, 0.25)',
  shadowColor: '#d946ef',
  shadowOpacity: 0.15,
};
```

### **Typography Integration**
```typescript
// Cairo/Tajawal font integration
const typography = {
  // Arabic text styles
  arabicTitle: {
    fontFamily: 'Cairo-Bold',
    fontSize: 24,
    lineHeight: 32,
    textAlign: 'right' as const,
  },
  arabicBody: {
    fontFamily: 'Cairo-Regular',
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'right' as const,
  },
  arabicCaption: {
    fontFamily: 'Tajawal-Regular',
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'right' as const,
  },
  
  // English text styles
  englishTitle: {
    fontFamily: 'Cairo-Bold',
    fontSize: 24,
    lineHeight: 32,
    textAlign: 'left' as const,
  },
  englishBody: {
    fontFamily: 'Cairo-Regular',
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'left' as const,
  },
};
```

### **Animation Standards**
```typescript
// Consistent animation timings
const animations = {
  fast: 200,
  normal: 300,
  slow: 500,
  
  // Easing curves
  easeOut: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  easeIn: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
  easeInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
  
  // Common animations
  fadeIn: {
    opacity: [0, 1],
    duration: 300,
  },
  slideUp: {
    translateY: [50, 0],
    opacity: [0, 1],
    duration: 400,
  },
  scaleIn: {
    scale: [0.8, 1],
    opacity: [0, 1],
    duration: 300,
  },
};
```

---

## 📋 **COMPONENT SPECIFICATIONS**

### **File Structure**
```
apps/mobile/src/components/ai/
├── VoiceRecordingButton.tsx
├── VoiceWaveform.tsx
├── VoiceMessageBubble.tsx
├── ImageUploadCard.tsx
├── ImageAnalysisOverlay.tsx
├── ImageMessageBubble.tsx
├── EnhancedChatInput.tsx
├── AIProcessingIndicator.tsx
├── ConfidenceScoreDisplay.tsx
├── AIInsightsPanel.tsx
├── index.ts
└── styles/
    ├── glassStyles.ts
    ├── typography.ts
    ├── animations.ts
    └── themes.ts
```

### **Shared Utilities**
**File**: `apps/mobile/src/components/ai/styles/glassStyles.ts`
- Glass morphism style generators
- Theme-aware style functions
- Responsive sizing utilities

**File**: `apps/mobile/src/components/ai/styles/typography.ts`
- Arabic/English font configurations
- RTL text handling utilities
- Responsive typography scales

**File**: `apps/mobile/src/components/ai/styles/animations.ts`
- Reusable animation configurations
- Gesture handling utilities
- Performance-optimized animations

---

## 🧪 **TESTING STRATEGY**

### **Component Testing**
```typescript
// Example test structure for each component
describe('VoiceRecordingButton', () => {
  it('should render with glass morphism styles', () => {});
  it('should handle recording state changes', () => {});
  it('should support both themes', () => {});
  it('should be accessible', () => {});
  it('should handle Arabic RTL layout', () => {});
});
```

### **Integration Testing**
- Voice recording flow end-to-end
- Image upload and analysis flow
- Chat interface with AI responses
- Theme switching functionality
- Arabic/English language switching

### **Performance Testing**
- 60fps animation validation
- Memory usage during recording
- Image processing performance
- Chat scrolling performance

---

## 📊 **SUCCESS CRITERIA**

### **Functional Requirements**
- [ ] All 8 components implemented and tested
- [ ] Voice recording works with AI transcription
- [ ] Image upload integrates with AI analysis
- [ ] Chat interface supports voice/image messages
- [ ] All components support dual themes
- [ ] Complete Arabic RTL support

### **Design Requirements**
- [ ] Glass morphism consistency with landing page
- [ ] Cairo/Tajawal typography integration
- [ ] Smooth 60fps animations
- [ ] Responsive design across screen sizes
- [ ] Accessibility compliance

### **Quality Gates**
- [ ] TypeScript compilation without errors
- [ ] All tests passing (>90% coverage)
- [ ] Performance benchmarks met
- [ ] Design review approval
- [ ] Arabic localization validation

---

## 🚀 **NEXT STEPS**

After completion of mobile UI components:
1. **Design System Integration**: Align with landing page standards
2. **Cross-Platform Testing**: Validate on iOS/Android
3. **Performance Optimization**: Ensure smooth user experience
4. **User Acceptance Testing**: Validate with Syrian users

**🎯 SUCCESS METRIC**: All AI features accessible through intuitive, culturally-appropriate mobile interface matching landing page design quality.

---

## 📋 **DETAILED COMPONENT SPECIFICATIONS**

### **Component 1: VoiceRecordingButton**
**File**: `apps/mobile/src/components/ai/VoiceRecordingButton.tsx`

```typescript
import React, { useEffect, useRef } from 'react';
import { View, TouchableOpacity, Animated, Vibration } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { voiceRecognitionService } from '../../services/voiceRecognitionService';

interface VoiceRecordingButtonProps {
  onStartRecording: () => void;
  onStopRecording: () => void;
  onRecordingComplete: (result: VoiceRecognitionResult) => void;
  isRecording: boolean;
  isProcessing: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  maxDuration?: number; // seconds
  style?: ViewStyle;
}

export const VoiceRecordingButton: React.FC<VoiceRecordingButtonProps> = ({
  onStartRecording,
  onStopRecording,
  onRecordingComplete,
  isRecording,
  isProcessing,
  disabled = false,
  size = 'medium',
  maxDuration = 60,
  style,
}) => {
  const { currentTheme } = useTheme();
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  // Size configurations
  const sizeConfig = {
    small: { diameter: 40, iconSize: 20 },
    medium: { diameter: 60, iconSize: 24 },
    large: { diameter: 80, iconSize: 32 },
  };

  const { diameter, iconSize } = sizeConfig[size];

  // Pulse animation during recording
  useEffect(() => {
    if (isRecording) {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => pulseAnimation.stop();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isRecording]);

  // Glow effect for processing
  useEffect(() => {
    if (isProcessing) {
      const glowAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(glowAnim, {
            toValue: 0,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );
      glowAnimation.start();

      return () => glowAnimation.stop();
    } else {
      glowAnim.setValue(0);
    }
  }, [isProcessing]);

  const handlePress = async () => {
    if (disabled) return;

    if (isRecording) {
      onStopRecording();
      try {
        const result = await voiceRecognitionService.stopRecording();
        if (result) {
          const transcription = await voiceRecognitionService.transcribeAudio(result);
          onRecordingComplete(transcription);
        }
      } catch (error) {
        console.error('Recording error:', error);
      }
    } else {
      Vibration.vibrate(50); // Haptic feedback
      onStartRecording();
      try {
        await voiceRecognitionService.startRecording({ maxDuration });
      } catch (error) {
        console.error('Recording start error:', error);
      }
    }
  };

  const buttonStyle = {
    width: diameter,
    height: diameter,
    borderRadius: diameter / 2,
    backgroundColor: currentTheme.colors.glass.background,
    borderWidth: 1,
    borderColor: currentTheme.colors.glass.border,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    shadowColor: currentTheme.colors.primary[500],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  };

  const getIconName = () => {
    if (isProcessing) return 'hourglass-outline';
    if (isRecording) return 'stop';
    return 'mic';
  };

  const getIconColor = () => {
    if (disabled) return currentTheme.colors.text.muted;
    if (isRecording) return '#ff4444';
    return currentTheme.colors.primary[500];
  };

  return (
    <Animated.View
      style={[
        buttonStyle,
        {
          transform: [{ scale: pulseAnim }],
          opacity: disabled ? 0.5 : 1,
        },
        style,
      ]}
    >
      <TouchableOpacity
        onPress={handlePress}
        disabled={disabled || isProcessing}
        style={{
          width: '100%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        activeOpacity={0.8}
      >
        <Animated.View
          style={{
            opacity: glowAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [1, 0.6],
            }),
          }}
        >
          <Ionicons
            name={getIconName()}
            size={iconSize}
            color={getIconColor()}
          />
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
};
```

### **Component 2: ImageUploadCard**
**File**: `apps/mobile/src/components/ai/ImageUploadCard.tsx`

```typescript
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { imageAnalysisService } from '../../services/imageAnalysisService';
import { AIProcessingIndicator } from './AIProcessingIndicator';

interface ImageUploadCardProps {
  onImageAnalyzed: (result: ImageAnalysisResult) => void;
  onError: (error: string) => void;
  style?: ViewStyle;
}

export const ImageUploadCard: React.FC<ImageUploadCardProps> = ({
  onImageAnalyzed,
  onError,
  style,
}) => {
  const { currentTheme } = useTheme();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<ImageAnalysisResult | null>(null);

  const handleImageSelection = (source: 'camera' | 'gallery') => {
    Alert.alert(
      'اختيار الصورة',
      'من أين تريد اختيار الصورة؟',
      [
        { text: 'الكاميرا', onPress: () => selectImage('camera') },
        { text: 'المعرض', onPress: () => selectImage('gallery') },
        { text: 'إلغاء', style: 'cancel' },
      ]
    );
  };

  const selectImage = async (source: 'camera' | 'gallery') => {
    try {
      const imageUri = await imageAnalysisService.pickImage(source);
      if (imageUri) {
        setSelectedImage(imageUri);
        await analyzeImage(imageUri);
      }
    } catch (error) {
      onError('فشل في اختيار الصورة');
    }
  };

  const analyzeImage = async (imageUri: string) => {
    setIsAnalyzing(true);
    try {
      const result = await imageAnalysisService.analyzePortfolioImage(imageUri);
      setAnalysisResult(result);
      onImageAnalyzed(result);
    } catch (error) {
      onError('فشل في تحليل الصورة');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const cardStyle = {
    backgroundColor: currentTheme.colors.glass.background,
    borderWidth: 1,
    borderColor: currentTheme.colors.glass.border,
    borderRadius: 16,
    padding: 20,
    minHeight: 200,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    shadowColor: currentTheme.colors.primary[500],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  };

  if (selectedImage && !isAnalyzing && analysisResult) {
    return (
      <View style={[cardStyle, style]}>
        <Image
          source={{ uri: selectedImage }}
          style={{
            width: '100%',
            height: 150,
            borderRadius: 12,
            marginBottom: 12,
          }}
          resizeMode="cover"
        />

        <View style={{ alignItems: 'center' }}>
          <Text style={{
            fontFamily: 'Cairo-SemiBold',
            fontSize: 16,
            color: currentTheme.colors.text.primary,
            textAlign: 'center',
            marginBottom: 8,
          }}>
            نتائج التحليل
          </Text>

          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
            <Text style={{
              fontFamily: 'Cairo-Regular',
              fontSize: 14,
              color: currentTheme.colors.text.secondary,
            }}>
              جودة العمل:
            </Text>
            <Text style={{
              fontFamily: 'Cairo-Bold',
              fontSize: 14,
              color: currentTheme.colors.primary[500],
              marginLeft: 4,
            }}>
              {Math.round(analysisResult.qualityScore * 100)}%
            </Text>
          </View>

          <Text style={{
            fontFamily: 'Cairo-Regular',
            fontSize: 12,
            color: currentTheme.colors.text.muted,
            textAlign: 'center',
          }}>
            المهارات المكتشفة: {analysisResult.skills.slice(0, 3).join('، ')}
          </Text>
        </View>

        <TouchableOpacity
          onPress={() => {
            setSelectedImage(null);
            setAnalysisResult(null);
          }}
          style={{
            marginTop: 12,
            paddingHorizontal: 16,
            paddingVertical: 8,
            backgroundColor: currentTheme.colors.primary[500],
            borderRadius: 8,
          }}
        >
          <Text style={{
            fontFamily: 'Cairo-SemiBold',
            fontSize: 14,
            color: '#fff',
          }}>
            اختيار صورة أخرى
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (isAnalyzing) {
    return (
      <View style={[cardStyle, style]}>
        {selectedImage && (
          <Image
            source={{ uri: selectedImage }}
            style={{
              width: '100%',
              height: 120,
              borderRadius: 12,
              marginBottom: 16,
              opacity: 0.7,
            }}
            resizeMode="cover"
          />
        )}

        <AIProcessingIndicator
          type="image"
          message="جاري تحليل الصورة..."
          size="medium"
        />
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={[cardStyle, style]}
      onPress={() => handleImageSelection('gallery')}
      activeOpacity={0.8}
    >
      <Ionicons
        name="cloud-upload-outline"
        size={48}
        color={currentTheme.colors.primary[500]}
        style={{ marginBottom: 12 }}
      />

      <Text style={{
        fontFamily: 'Cairo-SemiBold',
        fontSize: 18,
        color: currentTheme.colors.text.primary,
        textAlign: 'center',
        marginBottom: 8,
      }}>
        رفع صورة للتحليل
      </Text>

      <Text style={{
        fontFamily: 'Cairo-Regular',
        fontSize: 14,
        color: currentTheme.colors.text.secondary,
        textAlign: 'center',
        lineHeight: 20,
      }}>
        اختر صورة من أعمالك لتحليلها{'\n'}واستخراج المهارات والتقييم
      </Text>

      <View style={{
        flexDirection: 'row',
        marginTop: 16,
        gap: 12,
      }}>
        <TouchableOpacity
          onPress={() => selectImage('camera')}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 12,
            paddingVertical: 8,
            backgroundColor: currentTheme.colors.glass.background,
            borderRadius: 8,
            borderWidth: 1,
            borderColor: currentTheme.colors.glass.border,
          }}
        >
          <Ionicons
            name="camera"
            size={16}
            color={currentTheme.colors.primary[500]}
            style={{ marginRight: 4 }}
          />
          <Text style={{
            fontFamily: 'Cairo-Regular',
            fontSize: 12,
            color: currentTheme.colors.text.primary,
          }}>
            كاميرا
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => selectImage('gallery')}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 12,
            paddingVertical: 8,
            backgroundColor: currentTheme.colors.glass.background,
            borderRadius: 8,
            borderWidth: 1,
            borderColor: currentTheme.colors.glass.border,
          }}
        >
          <Ionicons
            name="images"
            size={16}
            color={currentTheme.colors.primary[500]}
            style={{ marginRight: 4 }}
          />
          <Text style={{
            fontFamily: 'Cairo-Regular',
            fontSize: 12,
            color: currentTheme.colors.text.primary,
          }}>
            المعرض
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};
```

---

## 🎨 **STYLING SYSTEM INTEGRATION**

### **Theme-Aware Styling Hook**
**File**: `apps/mobile/src/hooks/useComponentStyles.ts`

```typescript
import { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

export const useComponentStyles = () => {
  const { currentTheme } = useTheme();

  return useMemo(() => StyleSheet.create({
    // Glass morphism base styles
    glassContainer: {
      backgroundColor: currentTheme.colors.glass.background,
      borderWidth: 1,
      borderColor: currentTheme.colors.glass.border,
      borderRadius: 16,
      shadowColor: currentTheme.colors.primary[500],
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },

    glassPremium: {
      backgroundColor: currentTheme.colors.glass.background,
      borderWidth: 1,
      borderColor: currentTheme.colors.glass.border,
      borderRadius: 20,
      shadowColor: currentTheme.colors.primary[500],
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.15,
      shadowRadius: 16,
      elevation: 8,
    },

    // Typography styles
    titleArabic: {
      fontFamily: 'Cairo-Bold',
      fontSize: 24,
      lineHeight: 32,
      color: currentTheme.colors.text.primary,
      textAlign: 'right',
    },

    headingArabic: {
      fontFamily: 'Cairo-SemiBold',
      fontSize: 18,
      lineHeight: 24,
      color: currentTheme.colors.text.primary,
      textAlign: 'right',
    },

    bodyArabic: {
      fontFamily: 'Cairo-Regular',
      fontSize: 16,
      lineHeight: 24,
      color: currentTheme.colors.text.secondary,
      textAlign: 'right',
    },

    captionArabic: {
      fontFamily: 'Tajawal-Regular',
      fontSize: 14,
      lineHeight: 20,
      color: currentTheme.colors.text.muted,
      textAlign: 'right',
    },

    // Button styles
    primaryButton: {
      backgroundColor: currentTheme.colors.primary[500],
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
    },

    glassButton: {
      backgroundColor: currentTheme.colors.glass.background,
      borderWidth: 1,
      borderColor: currentTheme.colors.glass.border,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
    },

    // Input styles
    glassInput: {
      backgroundColor: currentTheme.colors.glass.background,
      borderWidth: 1,
      borderColor: currentTheme.colors.glass.border,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontFamily: 'Cairo-Regular',
      fontSize: 16,
      color: currentTheme.colors.text.primary,
      textAlign: 'right',
    },

    // Animation containers
    fadeContainer: {
      opacity: 1,
    },

    scaleContainer: {
      transform: [{ scale: 1 }],
    },
  }), [currentTheme]);
};
```

### **Animation Utilities**
**File**: `apps/mobile/src/utils/animations.ts`

```typescript
import { Animated, Easing } from 'react-native';

export const createFadeInAnimation = (animatedValue: Animated.Value, duration = 300) => {
  return Animated.timing(animatedValue, {
    toValue: 1,
    duration,
    easing: Easing.out(Easing.cubic),
    useNativeDriver: true,
  });
};

export const createScaleAnimation = (animatedValue: Animated.Value, toValue = 1, duration = 300) => {
  return Animated.spring(animatedValue, {
    toValue,
    tension: 100,
    friction: 8,
    useNativeDriver: true,
  });
};

export const createShimmerAnimation = (animatedValue: Animated.Value) => {
  return Animated.loop(
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    ])
  );
};

export const createPulseAnimation = (animatedValue: Animated.Value) => {
  return Animated.loop(
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 1.1,
        duration: 500,
        easing: Easing.inOut(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 500,
        easing: Easing.inOut(Easing.cubic),
        useNativeDriver: true,
      }),
    ])
  );
};
```

---

## 📱 **INTEGRATION EXAMPLES**

### **Enhanced AI Chat Screen Integration**
**File**: `apps/mobile/src/screens/chat/EnhancedAIChatScreen.tsx`

```typescript
import React, { useState } from 'react';
import { View, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { VoiceRecordingButton } from '../../components/ai/VoiceRecordingButton';
import { ImageUploadCard } from '../../components/ai/ImageUploadCard';
import { EnhancedChatInput } from '../../components/ai/EnhancedChatInput';
import { useTheme } from '../../contexts/ThemeContext';

export const EnhancedAIChatScreen: React.FC = () => {
  const { currentTheme } = useTheme();
  const [messages, setMessages] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleVoiceRecordingComplete = (result: VoiceRecognitionResult) => {
    // Add voice message to chat
    const voiceMessage = {
      id: Date.now().toString(),
      type: 'voice',
      content: result.transcript,
      confidence: result.confidence,
      timestamp: new Date(),
      isOwn: true,
    };
    setMessages(prev => [...prev, voiceMessage]);
  };

  const handleImageAnalyzed = (result: ImageAnalysisResult) => {
    // Add image message to chat
    const imageMessage = {
      id: Date.now().toString(),
      type: 'image',
      content: `تم تحليل الصورة: ${result.skills.join('، ')}`,
      analysis: result,
      timestamp: new Date(),
      isOwn: true,
    };
    setMessages(prev => [...prev, imageMessage]);
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: currentTheme.colors.background }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={{ flex: 1, padding: 16 }}>
          {/* Chat messages */}
          {messages.map(message => (
            <MessageBubble key={message.id} message={message} />
          ))}
        </ScrollView>

        <View style={{
          padding: 16,
          backgroundColor: currentTheme.colors.glass.background,
          borderTopWidth: 1,
          borderTopColor: currentTheme.colors.glass.border,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'flex-end',
            gap: 12,
          }}>
            <VoiceRecordingButton
              onStartRecording={() => setIsRecording(true)}
              onStopRecording={() => setIsRecording(false)}
              onRecordingComplete={handleVoiceRecordingComplete}
              isRecording={isRecording}
              isProcessing={isProcessing}
              size="medium"
            />

            <EnhancedChatInput
              onSend={(message) => {
                // Handle text message
              }}
              onImageUpload={() => {
                // Show image upload modal
              }}
              style={{ flex: 1 }}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
```

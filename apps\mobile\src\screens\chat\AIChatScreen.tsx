/**
 * AI Chat Screen
 * Real-time AI conversation interface with glass morphism design
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../../contexts/ThemeContext';
import { aiChatService, ChatMessage, ConversationSession } from '../../services/aiChat';
import { authStore } from '../../store/authStore';
import { showToast } from '../../utils/toast';

const { width, height } = Dimensions.get('window');

interface AIChatScreenProps {
  navigation: any;
  route: {
    params?: {
      sessionId?: string;
      userRole?: 'CLIENT' | 'EXPERT';
      language?: 'ar' | 'en';
      sessionType?: 'onboarding' | 'profile_optimization' | 'service_creation';
    };
  };
}

const AIChatScreen: React.FC<AIChatScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { user } = authStore();
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isConnecting, setIsConnecting] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [currentSession, setCurrentSession] = useState<ConversationSession | null>(null);
  
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Get params from route
  const {
    sessionId,
    userRole = user?.role as 'CLIENT' | 'EXPERT' || 'CLIENT',
    language = 'ar',
    sessionType = 'onboarding',
  } = route.params || {};

  /**
   * Initialize AI chat service
   */
  useEffect(() => {
    initializeChat();
    
    return () => {
      aiChatService.disconnect();
    };
  }, []);

  /**
   * Setup event listeners
   */
  useEffect(() => {
    const unsubscribeMessage = aiChatService.onMessage(handleNewMessage);
    const unsubscribeSession = aiChatService.onSessionUpdate(handleSessionUpdate);
    const unsubscribeTyping = aiChatService.onTyping(handleTypingIndicator);
    const unsubscribeError = aiChatService.onError(handleError);

    return () => {
      unsubscribeMessage();
      unsubscribeSession();
      unsubscribeTyping();
      unsubscribeError();
    };
  }, []);

  /**
   * Initialize chat connection
   */
  const initializeChat = async () => {
    try {
      setIsConnecting(true);
      
      // Connect to AI chat service
      await aiChatService.connect();
      
      if (sessionId) {
        // Join existing session
        await aiChatService.joinSession(sessionId);
      } else {
        // Start new conversation
        await aiChatService.startConversation({
          userRole,
          language,
          sessionType,
        });
      }
      
      setIsConnecting(false);
    } catch (error: any) {
      console.error('Failed to initialize AI chat:', error);
      setIsConnecting(false);
      showToast('error', 'فشل في بدء المحادثة', 'Failed to start conversation');
      
      Alert.alert(
        'خطأ في الاتصال',
        'فشل في الاتصال بخدمة المحادثة الذكية. يرجى المحاولة مرة أخرى.',
        [
          { text: 'إعادة المحاولة', onPress: initializeChat },
          { text: 'إلغاء', onPress: () => navigation.goBack() },
        ]
      );
    }
  };

  /**
   * Handle new message
   */
  const handleNewMessage = useCallback((message: ChatMessage) => {
    setMessages(prev => [...prev, message]);
    
    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  }, []);

  /**
   * Handle session update
   */
  const handleSessionUpdate = useCallback((session: ConversationSession) => {
    setCurrentSession(session);
    setMessages(session.messages);
    
    // Update navigation title with current step
    navigation.setOptions({
      title: getStepTitle(session.currentStep, session.language),
    });
    
    // Show completion message if session is completed
    if (session.isCompleted) {
      showToast('success', 'تم إكمال المحادثة', 'Conversation completed');
    }
  }, [navigation]);

  /**
   * Handle typing indicator
   */
  const handleTypingIndicator = useCallback((typing: boolean) => {
    setIsTyping(typing);
  }, []);

  /**
   * Handle errors
   */
  const handleError = useCallback((error: string) => {
    console.error('AI Chat error:', error);
    showToast('error', 'خطأ في المحادثة', error);
  }, []);

  /**
   * Send message
   */
  const sendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const messageText = inputText.trim();
    setInputText('');
    setIsLoading(true);

    try {
      await aiChatService.sendMessage(messageText);
    } catch (error: any) {
      console.error('Failed to send message:', error);
      showToast('error', 'فشل في إرسال الرسالة', 'Failed to send message');
      setInputText(messageText); // Restore message
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle typing
   */
  const handleTyping = (text: string) => {
    setInputText(text);
    
    // Send typing indicator
    aiChatService.sendTyping(true);
    
    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      aiChatService.sendTyping(false);
    }, 1000);
  };

  /**
   * Get step title
   */
  const getStepTitle = (step: string, lang: string): string => {
    const titles: Record<string, Record<string, string>> = {
      welcome: { ar: 'مرحباً', en: 'Welcome' },
      project_type: { ar: 'نوع المشروع', en: 'Project Type' },
      skills_experience: { ar: 'المهارات والخبرة', en: 'Skills & Experience' },
      summary: { ar: 'الملخص', en: 'Summary' },
      completion: { ar: 'اكتمل', en: 'Completed' },
    };
    
    return titles[step]?.[lang] || 'AI Assistant';
  };

  /**
   * Render message item
   */
  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isUser = item.type === 'user';
    const isRTL = currentSession?.language === 'ar';

    return (
      <View style={[
        styles.messageContainer,
        isUser ? styles.userMessageContainer : styles.aiMessageContainer,
        isRTL && styles.rtlMessage,
      ]}>
        <LinearGradient
          colors={isUser 
            ? [theme.colors.primary + '20', theme.colors.primary + '10']
            : [theme.colors.surface + '40', theme.colors.surface + '20']
          }
          style={[
            styles.messageBubble,
            isUser ? styles.userBubble : styles.aiBubble,
          ]}
        >
          <Text style={[
            styles.messageText,
            { color: theme.colors.onSurface },
            isRTL && styles.rtlText,
          ]}>
            {item.content}
          </Text>
          
          <Text style={[
            styles.messageTime,
            { color: theme.colors.onSurface + '60' },
          ]}>
            {new Date(item.timestamp).toLocaleTimeString('ar-SA', {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </LinearGradient>
      </View>
    );
  };

  /**
   * Render typing indicator
   */
  const renderTypingIndicator = () => {
    if (!isTyping) return null;

    return (
      <View style={[styles.messageContainer, styles.aiMessageContainer]}>
        <LinearGradient
          colors={[theme.colors.surface + '40', theme.colors.surface + '20']}
          style={[styles.messageBubble, styles.aiBubble]}
        >
          <View style={styles.typingContainer}>
            <ActivityIndicator size="small" color={theme.colors.primary} />
            <Text style={[
              styles.typingText,
              { color: theme.colors.onSurface + '80' },
            ]}>
              المساعد يكتب...
            </Text>
          </View>
        </LinearGradient>
      </View>
    );
  };

  if (isConnecting) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            جاري الاتصال بالمساعد الذكي...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <KeyboardAvoidingView 
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Messages List */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={renderTypingIndicator}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
        />

        {/* Input Container */}
        <LinearGradient
          colors={[theme.colors.surface + '40', theme.colors.surface + '20']}
          style={styles.inputContainer}
        >
          <View style={styles.inputWrapper}>
            <TextInput
              style={[
                styles.textInput,
                { 
                  color: theme.colors.onSurface,
                  borderColor: theme.colors.outline + '30',
                },
              ]}
              value={inputText}
              onChangeText={handleTyping}
              placeholder="اكتب رسالتك هنا..."
              placeholderTextColor={theme.colors.onSurface + '60'}
              multiline
              maxLength={2000}
              textAlign="right"
              editable={!isLoading}
            />
            
            <TouchableOpacity
              style={[
                styles.sendButton,
                { 
                  backgroundColor: inputText.trim() ? theme.colors.primary : theme.colors.outline + '30',
                },
                isLoading && styles.sendButtonDisabled,
              ]}
              onPress={sendMessage}
              disabled={!inputText.trim() || isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color={theme.colors.onPrimary} />
              ) : (
                <Icon 
                  name="send" 
                  size={20} 
                  color={inputText.trim() ? theme.colors.onPrimary : theme.colors.onSurface + '60'} 
                />
              )}
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 8,
  },
  messageContainer: {
    marginVertical: 4,
    maxWidth: width * 0.8,
  },
  userMessageContainer: {
    alignSelf: 'flex-end',
  },
  aiMessageContainer: {
    alignSelf: 'flex-start',
  },
  rtlMessage: {
    alignSelf: 'flex-end',
  },
  messageBubble: {
    padding: 12,
    borderRadius: 16,
    backdropFilter: 'blur(10px)',
  },
  userBubble: {
    borderBottomRightRadius: 4,
  },
  aiBubble: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 4,
  },
  rtlText: {
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  messageTime: {
    fontSize: 12,
    textAlign: 'right',
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingText: {
    marginLeft: 8,
    fontSize: 14,
    fontStyle: 'italic',
  },
  inputContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
});

export default AIChatScreen;

/**
 * OpenRouter API Integration Service
 * Handles AI model interactions for Freela Syria onboarding system
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';

// OpenRouter API Configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';

// Supported AI Models for different use cases
export const AI_MODELS = {
  GPT_4_TURBO: 'openai/gpt-4-turbo-preview',
  CLAUDE_3_SONNET: 'anthropic/claude-3-sonnet-20240229',
  GEMINI_PRO: 'google/gemini-pro',
  GPT_3_5_TURBO: 'openai/gpt-3.5-turbo',
} as const;

export type AIModel = typeof AI_MODELS[keyof typeof AI_MODELS];

// Message types for conversation
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp?: Date;
}

export interface AIResponse {
  content: string;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  finish_reason: string;
}

export interface ConversationContext {
  userId: string;
  sessionId: string;
  userRole: 'CLIENT' | 'EXPERT';
  language: 'ar' | 'en';
  currentStep: string;
  extractedData: Record<string, any>;
}

class OpenRouterService {
  private client: AxiosInstance;
  private defaultModel: AIModel = AI_MODELS.GPT_4_TURBO;

  constructor() {
    this.client = axios.create({
      baseURL: OPENROUTER_API_URL,
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://freela-syria.com',
        'X-Title': 'Freela Syria AI Onboarding',
      },
      timeout: 30000, // 30 seconds timeout
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.info('OpenRouter API Request', {
          url: config.url,
          method: config.method,
          model: config.data?.model,
        });
        return config;
      },
      (error) => {
        logger.error('OpenRouter API Request Error', { error });
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.info('OpenRouter API Response', {
          status: response.status,
          usage: response.data?.usage,
        });
        return response;
      },
      (error) => {
        logger.error('OpenRouter API Response Error', {
          status: error.response?.status,
          message: error.response?.data?.error?.message,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Send a chat completion request to OpenRouter
   */
  async chatCompletion(
    messages: ChatMessage[],
    context: ConversationContext,
    options: {
      model?: AIModel;
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    } = {}
  ): Promise<AIResponse> {
    try {
      const {
        model = this.defaultModel,
        temperature = 0.7,
        maxTokens = 1000,
        stream = false,
      } = options;

      // Add system context for Arabic/Syrian market
      const systemMessage = this.buildSystemMessage(context);
      const fullMessages = [systemMessage, ...messages];

      const response: AxiosResponse = await this.client.post('/chat/completions', {
        model,
        messages: fullMessages,
        temperature,
        max_tokens: maxTokens,
        stream,
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1,
      });

      const choice = response.data.choices[0];
      
      return {
        content: choice.message.content,
        model: response.data.model,
        usage: response.data.usage,
        finish_reason: choice.finish_reason,
      };

    } catch (error: any) {
      logger.error('OpenRouter chat completion error', {
        error: error.message,
        context,
        messages: messages.length,
      });

      if (error.response?.status === 429) {
        throw createError.tooManyRequests('AI service rate limit exceeded. Please try again later.');
      } else if (error.response?.status === 401) {
        throw createError.unauthorized('AI service authentication failed.');
      } else if (error.response?.status >= 500) {
        throw createError.internalServerError('AI service temporarily unavailable.');
      } else {
        throw createError.badRequest('Failed to process AI request.');
      }
    }
  }

  /**
   * Build system message with Syrian market context
   */
  private buildSystemMessage(context: ConversationContext): ChatMessage {
    const { userRole, language, currentStep } = context;
    
    const systemPrompts = {
      ar: {
        CLIENT: `أنت مساعد ذكي لمنصة فريلا سوريا، منصة العمل الحر الرائدة في سوريا. مهمتك مساعدة العملاء في إعداد ملفاتهم الشخصية وإيجاد الخبراء المناسبين لمشاريعهم.

السياق السوري:
- تفهم الثقافة السورية والتحديات المحلية
- تدعم اللغة العربية بطلاقة مع فهم اللهجة السورية
- تراعي الظروف الاقتصادية والاجتماعية في سوريا
- تقدم حلول عملية ومناسبة للسوق السوري

مهامك:
1. فهم احتياجات العميل ونوع المشروع المطلوب
2. استخراج المعلومات المهمة (الميزانية، الجدول الزمني، المتطلبات)
3. اقتراح فئات الخدمات المناسبة
4. تقديم نصائح لكتابة وصف مشروع فعال

كن ودودًا ومفيدًا ومهنيًا في تفاعلك.`,

        EXPERT: `أنت مساعد ذكي لمنصة فريلا سوريا، منصة العمل الحر الرائدة في سوريا. مهمتك مساعدة الخبراء في إعداد ملفاتهم المهنية وخدماتهم بطريقة احترافية.

السياق السوري:
- تفهم سوق العمل السوري والمهارات المطلوبة
- تدعم اللغة العربية بطلاقة مع فهجة اللهجة السورية
- تراعي التحديات المهنية في سوريا
- تقدم استراتيجيات تسويق مناسبة للسوق المحلي والعربي

مهامك:
1. استخراج المهارات والخبرات المهنية
2. تحسين وصف الملف الشخصي
3. اقتراح خدمات مربحة ومطلوبة
4. تحديد الأسعار المناسبة للسوق
5. تقديم نصائح لبناء سمعة مهنية قوية

كن محفزًا ومشجعًا ومهنيًا في تفاعلك.`
      },
      en: {
        CLIENT: `You are an AI assistant for Freela Syria, the leading freelance marketplace in Syria. Your mission is to help clients set up their profiles and find the right experts for their projects.

Syrian Context:
- Understand Syrian culture and local challenges
- Support Arabic language fluently with Syrian dialect understanding
- Consider economic and social conditions in Syria
- Provide practical solutions suitable for the Syrian market

Your tasks:
1. Understand client needs and project type
2. Extract important information (budget, timeline, requirements)
3. Suggest appropriate service categories
4. Provide tips for writing effective project descriptions

Be friendly, helpful, and professional in your interactions.`,

        EXPERT: `You are an AI assistant for Freela Syria, the leading freelance marketplace in Syria. Your mission is to help experts set up their professional profiles and services professionally.

Syrian Context:
- Understand the Syrian job market and required skills
- Support Arabic language fluently with Syrian dialect understanding
- Consider professional challenges in Syria
- Provide marketing strategies suitable for local and Arab markets

Your tasks:
1. Extract professional skills and experiences
2. Improve profile descriptions
3. Suggest profitable and in-demand services
4. Determine appropriate market pricing
5. Provide tips for building a strong professional reputation

Be motivating, encouraging, and professional in your interactions.`
      }
    };

    return {
      role: 'system',
      content: systemPrompts[language][userRole],
      timestamp: new Date(),
    };
  }

  /**
   * Generate a simple response (compatibility method)
   */
  async generateResponse(
    prompt: string,
    options: {
      model?: AIModel;
      temperature?: number;
      maxTokens?: number;
    } = {}
  ): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'user',
        content: prompt,
        timestamp: new Date(),
      }
    ];

    const context: ConversationContext = {
      userId: 'system',
      sessionId: 'system',
      userRole: 'CLIENT',
      language: 'ar',
      currentStep: 'general',
      extractedData: {},
    };

    const response = await this.chatCompletion(messages, context, options);
    return response.content;
  }

  /**
   * Test API connectivity and model availability
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/models');
      logger.info('OpenRouter connection test successful', {
        modelsCount: response.data?.data?.length || 0,
      });
      return true;
    } catch (error: any) {
      logger.error('OpenRouter connection test failed', { error: error.message });
      return false;
    }
  }

  /**
   * Get available models
   */
  async getAvailableModels(): Promise<any[]> {
    try {
      const response = await this.client.get('/models');
      return response.data?.data || [];
    } catch (error: any) {
      logger.error('Failed to fetch available models', { error: error.message });
      return [];
    }
  }
}

// Export singleton instance
export const openRouterService = new OpenRouterService();

// Export class for type imports
export { OpenRouterService };

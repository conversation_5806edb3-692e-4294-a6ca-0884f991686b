/**
 * AI Conversation Service
 * Manages AI-powered onboarding conversations for Freela Syria
 */

import { openRouterService, ChatMessage, ConversationContext, AIResponse } from './openrouter';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';
import { nanoid } from 'nanoid';

// Conversation flow steps for different user types
export const CONVERSATION_STEPS = {
  CLIENT: {
    WELCOME: 'welcome',
    PROJECT_TYPE: 'project_type',
    PROJECT_DETAILS: 'project_details',
    BUDGET_TIMELINE: 'budget_timeline',
    REQUIREMENTS: 'requirements',
    PREFERENCES: 'preferences',
    SUMMARY: 'summary',
    COMPLETION: 'completion',
  },
  EXPERT: {
    WELCOME: 'welcome',
    SKILLS_EXPERIENCE: 'skills_experience',
    SERVICES_OFFERED: 'services_offered',
    PRICING_STRATEGY: 'pricing_strategy',
    PORTFOLIO: 'portfolio',
    AVAILABILITY: 'availability',
    MARKET_POSITIONING: 'market_positioning',
    SUMMARY: 'summary',
    COMPLETION: 'completion',
  },
} as const;

export interface ConversationSession {
  id: string;
  userId: string;
  sessionType: 'onboarding' | 'profile_optimization' | 'service_creation';
  userRole: 'CLIENT' | 'EXPERT';
  language: 'ar' | 'en';
  currentStep: string;
  status: 'active' | 'completed' | 'abandoned' | 'paused';
  messages: ChatMessage[];
  extractedData: Record<string, any>;
  recommendations: any[];
  createdAt: Date;
  lastActiveAt: Date;
}

export interface ConversationMessage {
  id: string;
  sessionId: string;
  role: 'system' | 'user' | 'assistant';
  content: string;
  contentArabic?: string;
  stepName?: string;
  intent?: string;
  confidence?: number;
  createdAt: Date;
}

class AIConversationService {
  private sessions: Map<string, ConversationSession> = new Map();

  /**
   * Start a new AI conversation session
   */
  async startConversation(params: {
    userId: string;
    userRole: 'CLIENT' | 'EXPERT';
    language: 'ar' | 'en';
    sessionType?: 'onboarding' | 'profile_optimization' | 'service_creation';
  }): Promise<ConversationSession> {
    const { userId, userRole, language, sessionType = 'onboarding' } = params;

    const sessionId = nanoid();
    const session: ConversationSession = {
      id: sessionId,
      userId,
      sessionType,
      userRole,
      language,
      currentStep: CONVERSATION_STEPS[userRole].WELCOME,
      status: 'active',
      messages: [],
      extractedData: {},
      recommendations: [],
      createdAt: new Date(),
      lastActiveAt: new Date(),
    };

    // Store session in memory (in production, this would be stored in database)
    this.sessions.set(sessionId, session);

    // Generate welcome message
    const welcomeMessage = await this.generateWelcomeMessage(session);
    session.messages.push(welcomeMessage);

    logger.info('AI conversation session started', {
      sessionId,
      userId,
      userRole,
      language,
      sessionType,
    });

    return session;
  }

  /**
   * Process user message and generate AI response
   */
  async processMessage(
    sessionId: string,
    userMessage: string
  ): Promise<{ session: ConversationSession; aiResponse: ChatMessage }> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw createError.notFound('Conversation session not found');
    }

    if (session.status !== 'active') {
      throw createError.badRequest('Conversation session is not active');
    }

    // Add user message to session
    const userChatMessage: ChatMessage = {
      role: 'user',
      content: userMessage,
      timestamp: new Date(),
    };
    session.messages.push(userChatMessage);

    // Build conversation context
    const context: ConversationContext = {
      userId: session.userId,
      sessionId: session.id,
      userRole: session.userRole,
      language: session.language,
      currentStep: session.currentStep,
      extractedData: session.extractedData,
    };

    try {
      // Get AI response
      const aiResponse = await openRouterService.chatCompletion(
        session.messages,
        context,
        {
          temperature: 0.7,
          maxTokens: 800,
        }
      );

      // Create AI message
      const aiChatMessage: ChatMessage = {
        role: 'assistant',
        content: aiResponse.content,
        timestamp: new Date(),
      };

      // Add AI response to session
      session.messages.push(aiChatMessage);
      session.lastActiveAt = new Date();

      // Extract data from conversation
      await this.extractDataFromMessage(session, userMessage, aiResponse.content);

      // Determine next step
      await this.updateConversationStep(session);

      // Update session in storage
      this.sessions.set(sessionId, session);

      logger.info('AI message processed', {
        sessionId,
        currentStep: session.currentStep,
        messageCount: session.messages.length,
      });

      return { session, aiResponse: aiChatMessage };

    } catch (error: any) {
      logger.error('Error processing AI message', {
        sessionId,
        error: error.message,
      });
      throw createError.internalServerError('Failed to process message');
    }
  }

  /**
   * Get conversation session
   */
  getSession(sessionId: string): ConversationSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Get user's active sessions
   */
  getUserSessions(userId: string): ConversationSession[] {
    return Array.from(this.sessions.values()).filter(
      session => session.userId === userId
    );
  }

  /**
   * Generate welcome message based on user role and language
   */
  private async generateWelcomeMessage(session: ConversationSession): Promise<ChatMessage> {
    const { userRole, language } = session;

    const welcomePrompts = {
      ar: {
        CLIENT: `مرحباً بك في فريلا سوريا! 🎉

أنا مساعدك الذكي وسأساعدك في إعداد ملفك الشخصي وإيجاد أفضل الخبراء لمشروعك.

لنبدأ بسؤال بسيط: ما نوع المشروع الذي تريد العمل عليه؟

يمكنك أن تخبرني عن:
• نوع الخدمة المطلوبة (تصميم، برمجة، كتابة، ترجمة، إلخ)
• وصف مختصر للمشروع
• أي تفاصيل أخرى تريد مشاركتها

أنا هنا لأساعدك في كل خطوة! 😊`,

        EXPERT: `أهلاً وسهلاً بك في فريلا سوريا! 🌟

أنا مساعدك الذكي وسأساعدك في بناء ملف مهني قوي وإنشاء خدمات مميزة تجذب العملاء.

لنبدأ بالتعرف عليك أكثر: ما هي مهاراتك وخبراتك المهنية؟

يمكنك أن تخبرني عن:
• المجال الذي تعمل فيه
• سنوات الخبرة
• أهم المهارات التي تتقنها
• أي مشاريع سابقة عملت عليها

سأساعدك في تحويل خبراتك إلى خدمات مربحة! 💪`
      },
      en: {
        CLIENT: `Welcome to Freela Syria! 🎉

I'm your AI assistant and I'll help you set up your profile and find the best experts for your project.

Let's start with a simple question: What type of project do you want to work on?

You can tell me about:
• Type of service needed (design, programming, writing, translation, etc.)
• Brief project description
• Any other details you'd like to share

I'm here to help you every step of the way! 😊`,

        EXPERT: `Welcome to Freela Syria! 🌟

I'm your AI assistant and I'll help you build a strong professional profile and create outstanding services that attract clients.

Let's start by getting to know you better: What are your professional skills and experiences?

You can tell me about:
• Your field of work
• Years of experience
• Key skills you master
• Any previous projects you've worked on

I'll help you turn your expertise into profitable services! 💪`
      }
    };

    return {
      role: 'assistant',
      content: welcomePrompts[language][userRole],
      timestamp: new Date(),
    };
  }

  /**
   * Extract structured data from conversation messages
   */
  private async extractDataFromMessage(
    session: ConversationSession,
    userMessage: string,
    aiResponse: string
  ): Promise<void> {
    // This is a simplified version - in production, this would use more sophisticated NLP
    const { userRole, currentStep } = session;

    try {
      // Extract data based on current step and user role
      if (userRole === 'CLIENT') {
        await this.extractClientData(session, userMessage, currentStep);
      } else if (userRole === 'EXPERT') {
        await this.extractExpertData(session, userMessage, currentStep);
      }
    } catch (error: any) {
      logger.error('Error extracting data from message', {
        sessionId: session.id,
        currentStep,
        error: error.message,
      });
    }
  }

  /**
   * Extract data for client conversations
   */
  private async extractClientData(
    session: ConversationSession,
    userMessage: string,
    currentStep: string
  ): Promise<void> {
    const extractedData = session.extractedData;

    switch (currentStep) {
      case CONVERSATION_STEPS.CLIENT.PROJECT_TYPE:
        // Extract project type and category
        extractedData.projectType = this.extractProjectType(userMessage);
        break;

      case CONVERSATION_STEPS.CLIENT.BUDGET_TIMELINE:
        // Extract budget and timeline information
        const budgetInfo = this.extractBudgetInfo(userMessage);
        if (budgetInfo) {
          extractedData.budget = budgetInfo;
        }
        break;

      case CONVERSATION_STEPS.CLIENT.REQUIREMENTS:
        // Extract project requirements
        if (!extractedData.requirements) {
          extractedData.requirements = [];
        }
        extractedData.requirements.push(userMessage);
        break;
    }
  }

  /**
   * Extract data for expert conversations
   */
  private async extractExpertData(
    session: ConversationSession,
    userMessage: string,
    currentStep: string
  ): Promise<void> {
    const extractedData = session.extractedData;

    switch (currentStep) {
      case CONVERSATION_STEPS.EXPERT.SKILLS_EXPERIENCE:
        // Extract skills and experience
        extractedData.skills = this.extractSkills(userMessage);
        extractedData.experience = this.extractExperience(userMessage);
        break;

      case CONVERSATION_STEPS.EXPERT.SERVICES_OFFERED:
        // Extract service offerings
        if (!extractedData.services) {
          extractedData.services = [];
        }
        extractedData.services.push(userMessage);
        break;

      case CONVERSATION_STEPS.EXPERT.PRICING_STRATEGY:
        // Extract pricing information
        const pricingInfo = this.extractPricingInfo(userMessage);
        if (pricingInfo) {
          extractedData.pricing = pricingInfo;
        }
        break;
    }
  }

  /**
   * Update conversation step based on current progress
   */
  private async updateConversationStep(session: ConversationSession): Promise<void> {
    const { userRole, currentStep } = session;
    const steps = CONVERSATION_STEPS[userRole];
    const stepKeys = Object.values(steps);
    const currentIndex = stepKeys.indexOf(currentStep);

    // Move to next step if we have enough information
    if (currentIndex < stepKeys.length - 1) {
      session.currentStep = stepKeys[currentIndex + 1];
    } else {
      // Conversation completed
      session.status = 'completed';
    }
  }

  // Helper methods for data extraction (simplified versions)
  private extractProjectType(message: string): string {
    // Simple keyword matching - in production, use more sophisticated NLP
    const keywords = {
      'تصميم': 'design',
      'برمجة': 'programming',
      'كتابة': 'writing',
      'ترجمة': 'translation',
      'تسويق': 'marketing',
    };

    for (const [arabic, english] of Object.entries(keywords)) {
      if (message.includes(arabic) || message.toLowerCase().includes(english)) {
        return english;
      }
    }
    return 'other';
  }

  private extractBudgetInfo(message: string): any {
    // Extract budget information using regex
    const budgetRegex = /(\d+)\s*(دولار|dollar|\$|usd)/i;
    const match = message.match(budgetRegex);
    if (match) {
      return {
        amount: parseInt(match[1]),
        currency: 'USD',
      };
    }
    return null;
  }

  private extractSkills(message: string): string[] {
    // Extract skills from message
    const commonSkills = [
      'javascript', 'python', 'react', 'nodejs', 'php', 'html', 'css',
      'photoshop', 'illustrator', 'figma', 'wordpress', 'seo', 'marketing'
    ];

    return commonSkills.filter(skill => 
      message.toLowerCase().includes(skill)
    );
  }

  private extractExperience(message: string): any {
    // Extract years of experience
    const expRegex = /(\d+)\s*(سنة|سنوات|year|years)/i;
    const match = message.match(expRegex);
    if (match) {
      return {
        years: parseInt(match[1]),
        level: this.getExperienceLevel(parseInt(match[1])),
      };
    }
    return null;
  }

  private extractPricingInfo(message: string): any {
    // Extract pricing information
    const hourlyRegex = /(\d+)\s*(دولار|dollar|\$)\s*(ساعة|hour)/i;
    const match = message.match(hourlyRegex);
    if (match) {
      return {
        type: 'hourly',
        rate: parseInt(match[1]),
        currency: 'USD',
      };
    }
    return null;
  }

  private getExperienceLevel(years: number): string {
    if (years < 1) return 'beginner';
    if (years < 3) return 'intermediate';
    if (years < 7) return 'advanced';
    return 'expert';
  }
}

// Export singleton instance
export const aiConversationService = new AIConversationService();
